{"version": 3, "file": "polyfill.min.js", "sources": ["../src/stub/symbol.ts", "../node_modules/tslib/tslib.es6.js", "../src/utils.ts", "../src/lib/helpers/miscellaneous.ts", "../src/lib/helpers/webidl.ts", "../src/lib/simple-queue.ts", "../src/lib/abstract-ops/internal-methods.ts", "../src/lib/readable-stream/generic-reader.ts", "../src/stub/number-isfinite.ts", "../src/stub/math-trunc.ts", "../src/lib/validators/basic.ts", "../src/lib/validators/readable-stream.ts", "../src/lib/readable-stream/default-reader.ts", "../src/lib/abstract-ops/ecmascript.ts", "../src/target/es5/stub/async-iterator-prototype.ts", "../src/lib/readable-stream/async-iterator.ts", "../src/stub/number-isnan.ts", "../src/lib/abstract-ops/miscellaneous.ts", "../src/lib/abstract-ops/queue-with-sizes.ts", "../src/lib/helpers/array-buffer-view.ts", "../src/lib/readable-stream/byte-stream-controller.ts", "../src/lib/readable-stream/byob-reader.ts", "../src/lib/validators/reader-options.ts", "../src/lib/abstract-ops/queuing-strategy.ts", "../src/lib/validators/queuing-strategy.ts", "../src/lib/validators/underlying-sink.ts", "../src/lib/validators/writable-stream.ts", "../src/lib/abort-signal.ts", "../src/lib/writable-stream.ts", "../src/globals.ts", "../src/stub/dom-exception.ts", "../src/lib/readable-stream/pipe.ts", "../src/lib/readable-stream/default-controller.ts", "../src/lib/readable-stream/tee.ts", "../src/lib/readable-stream/from.ts", "../src/lib/readable-stream/readable-stream-like.ts", "../src/lib/validators/underlying-source.ts", "../src/lib/validators/pipe-options.ts", "../src/lib/readable-stream.ts", "../src/lib/validators/readable-writable-pair.ts", "../src/lib/validators/iterator-options.ts", "../src/lib/validators/queuing-strategy-init.ts", "../src/lib/byte-length-queuing-strategy.ts", "../src/lib/count-queuing-strategy.ts", "../src/lib/validators/transformer.ts", "../src/lib/transform-stream.ts", "../src/polyfill.ts"], "sourcesContent": ["/// <reference lib=\"es2015.symbol\" />\n\nconst SymbolPolyfill: (description?: string) => symbol =\n  typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ?\n    Symbol :\n    description => `Symbol(${description})` as any as symbol;\n\nexport default SymbolPolyfill;\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "export function noop(): undefined {\n  return undefined;\n}\n", "import { noop } from '../../utils';\nimport { AssertionError } from '../../stub/assert';\n\nexport function typeIsObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport const rethrowAssertionErrorRejection: (e: any) => void =\n  DEBUG ? e => {\n    // Used throughout the reference implementation, as `.catch(rethrowAssertionErrorRejection)`, to ensure any errors\n    // get shown. There are places in the spec where we do promise transformations and purposefully ignore or don't\n    // expect any errors, but assertion errors are always problematic.\n    if (e && e instanceof AssertionError) {\n      setTimeout(() => {\n        throw e;\n      }, 0);\n    }\n  } : noop;\n\nexport function setFunctionName(fn: Function, name: string): void {\n  try {\n    Object.defineProperty(fn, 'name', {\n      value: name,\n      configurable: true\n    });\n  } catch {\n    // This property is non-configurable in older browsers, so ignore if this throws.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/name#browser_compatibility\n  }\n}\n", "import { rethrowAssertionErrorRejection } from './miscellaneous';\nimport assert from '../../stub/assert';\n\nconst originalPromise = Promise;\nconst originalPromiseThen = Promise.prototype.then;\nconst originalPromiseReject = Promise.reject.bind(originalPromise);\n\n// https://webidl.spec.whatwg.org/#a-new-promise\nexport function newPromise<T>(executor: (\n  resolve: (value: T | PromiseLike<T>) => void,\n  reject: (reason?: any) => void\n) => void): Promise<T> {\n  return new originalPromise(executor);\n}\n\n// https://webidl.spec.whatwg.org/#a-promise-resolved-with\nexport function promiseResolvedWith<T>(value: T | PromiseLike<T>): Promise<T> {\n  return newPromise(resolve => resolve(value));\n}\n\n// https://webidl.spec.whatwg.org/#a-promise-rejected-with\nexport function promiseRejectedWith<T = never>(reason: any): Promise<T> {\n  return originalPromiseReject(reason);\n}\n\nexport function PerformPromiseThen<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  onRejected?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  // There doesn't appear to be any way to correctly emulate the behaviour from JavaScript, so this is just an\n  // approximation.\n  return originalPromiseThen.call(promise, onFulfilled, onRejected) as Promise<TResult1 | TResult2>;\n}\n\n// Bluebird logs a warning when a promise is created within a fulfillment handler, but then isn't returned\n// from that handler. To prevent this, return null instead of void from all handlers.\n// http://bluebirdjs.com/docs/warning-explanations.html#warning-a-promise-was-created-in-a-handler-but-was-not-returned-from-it\nexport function uponPromise<T>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => null | PromiseLike<null>,\n  onRejected?: (reason: any) => null | PromiseLike<null>): void {\n  PerformPromiseThen(\n    PerformPromiseThen(promise, onFulfilled, onRejected),\n    undefined,\n    rethrowAssertionErrorRejection\n  );\n}\n\nexport function uponFulfillment<T>(promise: Promise<T>, onFulfilled: (value: T) => null | PromiseLike<null>): void {\n  uponPromise(promise, onFulfilled);\n}\n\nexport function uponRejection(promise: Promise<unknown>, onRejected: (reason: any) => null | PromiseLike<null>): void {\n  uponPromise(promise, undefined, onRejected);\n}\n\nexport function transformPromiseWith<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  fulfillmentHandler?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  rejectionHandler?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  return PerformPromiseThen(promise, fulfillmentHandler, rejectionHandler);\n}\n\nexport function setPromiseIsHandledToTrue(promise: Promise<unknown>): void {\n  PerformPromiseThen(promise, undefined, rethrowAssertionErrorRejection);\n}\n\nlet _queueMicrotask: (callback: () => void) => void = callback => {\n  if (typeof queueMicrotask === 'function') {\n    _queueMicrotask = queueMicrotask;\n  } else {\n    const resolvedPromise = promiseResolvedWith(undefined);\n    _queueMicrotask = cb => PerformPromiseThen(resolvedPromise, cb);\n  }\n  return _queueMicrotask(callback);\n};\n\nexport { _queueMicrotask as queueMicrotask };\n\nexport function reflectCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R, V: T, args: A): R {\n  if (typeof F !== 'function') {\n    throw new TypeError('Argument is not a function');\n  }\n  return Function.prototype.apply.call(F, V, args);\n}\n\nexport function promiseCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R | PromiseLike<R>,\n                                                   V: T,\n                                                   args: A): Promise<R> {\n  assert(typeof F === 'function');\n  assert(V !== undefined);\n  assert(Array.isArray(args));\n  try {\n    return promiseResolvedWith(reflectCall(F, V, args));\n  } catch (value) {\n    return promiseRejectedWith(value);\n  }\n}\n", "import assert from '../stub/assert';\n\n// Original from Chromium\n// https://chromium.googlesource.com/chromium/src/+/0aee4434a4dba42a42abaea9bfbc0cd196a63bc1/third_party/blink/renderer/core/streams/SimpleQueue.js\n\nconst QUEUE_MAX_ARRAY_SIZE = 16384;\n\ninterface Node<T> {\n  _elements: T[];\n  _next: Node<T> | undefined;\n}\n\n/**\n * Simple queue structure.\n *\n * Avoids scalability issues with using a packed array directly by using\n * multiple arrays in a linked list and keeping the array size bounded.\n */\nexport class SimpleQueue<T> {\n  private _front: Node<T>;\n  private _back: Node<T>;\n  private _cursor = 0;\n  private _size = 0;\n\n  constructor() {\n    // _front and _back are always defined.\n    this._front = {\n      _elements: [],\n      _next: undefined\n    };\n    this._back = this._front;\n    // The cursor is used to avoid calling Array.shift().\n    // It contains the index of the front element of the array inside the\n    // front-most node. It is always in the range [0, QUEUE_MAX_ARRAY_SIZE).\n    this._cursor = 0;\n    // When there is only one node, size === elements.length - cursor.\n    this._size = 0;\n  }\n\n  get length(): number {\n    return this._size;\n  }\n\n  // For exception safety, this method is structured in order:\n  // 1. Read state\n  // 2. Calculate required state mutations\n  // 3. Perform state mutations\n  push(element: T): void {\n    const oldBack = this._back;\n    let newBack = oldBack;\n    assert(oldBack._next === undefined);\n    if (oldBack._elements.length === QUEUE_MAX_ARRAY_SIZE - 1) {\n      newBack = {\n        _elements: [],\n        _next: undefined\n      };\n    }\n\n    // push() is the mutation most likely to throw an exception, so it\n    // goes first.\n    oldBack._elements.push(element);\n    if (newBack !== oldBack) {\n      this._back = newBack;\n      oldBack._next = newBack;\n    }\n    ++this._size;\n  }\n\n  // Like push(), shift() follows the read -> calculate -> mutate pattern for\n  // exception safety.\n  shift(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const oldFront = this._front;\n    let newFront = oldFront;\n    const oldCursor = this._cursor;\n    let newCursor = oldCursor + 1;\n\n    const elements = oldFront._elements;\n    const element = elements[oldCursor];\n\n    if (newCursor === QUEUE_MAX_ARRAY_SIZE) {\n      assert(elements.length === QUEUE_MAX_ARRAY_SIZE);\n      assert(oldFront._next !== undefined);\n      newFront = oldFront._next!;\n      newCursor = 0;\n    }\n\n    // No mutations before this point.\n    --this._size;\n    this._cursor = newCursor;\n    if (oldFront !== newFront) {\n      this._front = newFront;\n    }\n\n    // Permit shifted element to be garbage collected.\n    elements[oldCursor] = undefined!;\n\n    return element;\n  }\n\n  // The tricky thing about forEach() is that it can be called\n  // re-entrantly. The queue may be mutated inside the callback. It is easy to\n  // see that push() within the callback has no negative effects since the end\n  // of the queue is checked for on every iteration. If shift() is called\n  // repeatedly within the callback then the next iteration may return an\n  // element that has been removed. In this case the callback will be called\n  // with undefined values until we either \"catch up\" with elements that still\n  // exist or reach the back of the queue.\n  forEach(callback: (element: T) => void): void {\n    let i = this._cursor;\n    let node = this._front;\n    let elements = node._elements;\n    while (i !== elements.length || node._next !== undefined) {\n      if (i === elements.length) {\n        assert(node._next !== undefined);\n        assert(i === QUEUE_MAX_ARRAY_SIZE);\n        node = node._next!;\n        elements = node._elements;\n        i = 0;\n        if (elements.length === 0) {\n          break;\n        }\n      }\n      callback(elements[i]);\n      ++i;\n    }\n  }\n\n  // Return the element that would be returned if shift() was called now,\n  // without modifying the queue.\n  peek(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const front = this._front;\n    const cursor = this._cursor;\n    return front._elements[cursor];\n  }\n}\n", "export const AbortSteps = Symbol('[[AbortSteps]]');\nexport const ErrorSteps = Symbol('[[ErrorSteps]]');\nexport const CancelSteps = Symbol('[[CancelSteps]]');\nexport const PullSteps = Symbol('[[PullSteps]]');\nexport const ReleaseSteps = Symbol('[[ReleaseSteps]]');\n", "import assert from '../../stub/assert';\nimport { ReadableStream, ReadableStreamCancel, type ReadableStreamReader } from '../readable-stream';\nimport { newPromise, setPromiseIsHandledToTrue } from '../helpers/webidl';\nimport { ReleaseSteps } from '../abstract-ops/internal-methods';\n\nexport function ReadableStreamReaderGenericInitialize<R>(reader: ReadableStreamReader<R>, stream: ReadableStream<R>) {\n  reader._ownerReadableStream = stream;\n  stream._reader = reader;\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseInitialize(reader);\n  } else if (stream._state === 'closed') {\n    defaultReaderClosedPromiseInitializeAsResolved(reader);\n  } else {\n    assert(stream._state === 'errored');\n\n    defaultReaderClosedPromiseInitializeAsRejected(reader, stream._storedError);\n  }\n}\n\n// A client of ReadableStreamDefaultReader and ReadableStreamBYO<PERSON>eader may use these functions directly to bypass state\n// check.\n\nexport function ReadableStreamReaderGenericCancel(reader: ReadableStreamReader<any>, reason: any): Promise<undefined> {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  return ReadableStreamCancel(stream, reason);\n}\n\nexport function ReadableStreamReaderGenericRelease(reader: ReadableStreamReader<any>) {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  assert(stream._reader === reader);\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseReject(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  } else {\n    defaultReaderClosedPromiseResetToRejected(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  }\n\n  stream._readableStreamController[ReleaseSteps]();\n\n  stream._reader = undefined;\n  reader._ownerReadableStream = undefined!;\n}\n\n// Helper functions for the readers.\n\nexport function readerLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released reader');\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nexport function defaultReaderClosedPromiseInitialize(reader: ReadableStreamReader<any>) {\n  reader._closedPromise = newPromise((resolve, reject) => {\n    reader._closedPromise_resolve = resolve;\n    reader._closedPromise_reject = reject;\n  });\n}\n\nexport function defaultReaderClosedPromiseInitializeAsRejected(reader: ReadableStreamReader<any>, reason: any) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseReject(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseInitializeAsResolved(reader: ReadableStreamReader<any>) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseResolve(reader);\n}\n\nexport function defaultReaderClosedPromiseReject(reader: ReadableStreamReader<any>, reason: any) {\n  if (reader._closedPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(reader._closedPromise);\n  reader._closedPromise_reject(reason);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n\nexport function defaultReaderClosedPromiseResetToRejected(reader: ReadableStreamReader<any>, reason: any) {\n  assert(reader._closedPromise_resolve === undefined);\n  assert(reader._closedPromise_reject === undefined);\n\n  defaultReaderClosedPromiseInitializeAsRejected(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseResolve(reader: ReadableStreamReader<any>) {\n  if (reader._closedPromise_resolve === undefined) {\n    return;\n  }\n\n  reader._closedPromise_resolve(undefined);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isFinite#Polyfill\nconst NumberIsFinite: typeof Number.isFinite = Number.isFinite || function (x) {\n  return typeof x === 'number' && isFinite(x);\n};\n\nexport default NumberIsFinite;\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc#Polyfill\nconst MathTrunc: typeof Math.trunc = Math.trunc || function (v) {\n  return v < 0 ? Math.ceil(v) : Math.floor(v);\n};\n\nexport default MathTrunc;\n", "import NumberIsFinite from '../../stub/number-isfinite';\nimport MathTrunc from '../../stub/math-trunc';\n\n// https://heycam.github.io/webidl/#idl-dictionaries\nexport function isDictionary(x: any): x is object | null {\n  return typeof x === 'object' || typeof x === 'function';\n}\n\nexport function assertDictionary(obj: unknown,\n                                 context: string): asserts obj is object | null | undefined {\n  if (obj !== undefined && !isDictionary(obj)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport type AnyFunction = (...args: any[]) => any;\n\n// https://heycam.github.io/webidl/#idl-callback-functions\nexport function assertFunction(x: unknown, context: string): asserts x is AnyFunction {\n  if (typeof x !== 'function') {\n    throw new TypeError(`${context} is not a function.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-object\nexport function isObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport function assertObject(x: unknown,\n                             context: string): asserts x is object {\n  if (!isObject(x)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport function assertRequiredArgument<T>(x: T | undefined,\n                                          position: number,\n                                          context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`Parameter ${position} is required in '${context}'.`);\n  }\n}\n\nexport function assertRequiredField<T>(x: T | undefined,\n                                       field: string,\n                                       context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`${field} is required in '${context}'.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-unrestricted-double\nexport function convertUnrestrictedDouble(value: unknown): number {\n  return Number(value);\n}\n\nfunction censorNegativeZero(x: number): number {\n  return x === 0 ? 0 : x;\n}\n\nfunction integerPart(x: number): number {\n  return censorNegativeZero(MathTrunc(x));\n}\n\n// https://heycam.github.io/webidl/#idl-unsigned-long-long\nexport function convertUnsignedLongLongWithEnforceRange(value: unknown, context: string): number {\n  const lowerBound = 0;\n  const upperBound = Number.MAX_SAFE_INTEGER;\n\n  let x = Number(value);\n  x = censorNegativeZero(x);\n\n  if (!NumberIsFinite(x)) {\n    throw new TypeError(`${context} is not a finite number`);\n  }\n\n  x = integerPart(x);\n\n  if (x < lowerBound || x > upperBound) {\n    throw new TypeError(`${context} is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`);\n  }\n\n  if (!NumberIsFinite(x) || x === 0) {\n    return 0;\n  }\n\n  // TODO Use BigInt if supported?\n  // let xBigInt = BigInt(integerPart(x));\n  // xBigInt = BigInt.asUintN(64, xBigInt);\n  // return Number(xBigInt);\n\n  return x;\n}\n", "import { IsReadableStream, ReadableStream } from '../readable-stream';\n\nexport function assertReadableStream(x: unknown, context: string): asserts x is ReadableStream {\n  if (!IsReadableStream(x)) {\n    throw new TypeError(`${context} is not a ReadableStream.`);\n  }\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableStream } from '../readable-stream';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { PullSteps } from '../abstract-ops/internal-methods';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\n\n/**\n * A result returned by {@link ReadableStreamDefaultReader.read}.\n *\n * @public\n */\nexport type ReadableStreamDefaultReadResult<T> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value?: undefined;\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamDefaultReader<R>(stream: ReadableStream): ReadableStreamDefaultReader<R> {\n  return new ReadableStreamDefaultReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadRequest<R>(stream: ReadableStream<R>,\n                                                readRequest: ReadRequest<R>): void {\n  assert(IsReadableStreamDefaultReader(stream._reader));\n  assert(stream._state === 'readable');\n\n  (stream._reader! as ReadableStreamDefaultReader<R>)._readRequests.push(readRequest);\n}\n\nexport function ReadableStreamFulfillReadRequest<R>(stream: ReadableStream<R>, chunk: R | undefined, done: boolean) {\n  const reader = stream._reader as ReadableStreamDefaultReader<R>;\n\n  assert(reader._readRequests.length > 0);\n\n  const readRequest = reader._readRequests.shift()!;\n  if (done) {\n    readRequest._closeSteps();\n  } else {\n    readRequest._chunkSteps(chunk!);\n  }\n}\n\nexport function ReadableStreamGetNumReadRequests<R>(stream: ReadableStream<R>): number {\n  return (stream._reader as ReadableStreamDefaultReader<R>)._readRequests.length;\n}\n\nexport function ReadableStreamHasDefaultReader(stream: ReadableStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamDefaultReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadRequest<R> {\n  _chunkSteps(chunk: R): void;\n\n  _closeSteps(): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A default reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamDefaultReader<R = any> {\n  /** @internal */\n  _ownerReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readRequests: SimpleQueue<ReadRequest<R>>;\n\n  constructor(stream: ReadableStream<R>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamDefaultReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed,\n   * or rejected if the stream ever errors or the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Returns a promise that allows access to the next chunk from the stream's internal queue, if available.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('read'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: () => resolvePromise({ value: undefined, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamDefaultReaderRead(this, readRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamDefaultReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamDefaultReader(this)) {\n      throw defaultReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    ReadableStreamDefaultReaderRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nsetFunctionName(ReadableStreamDefaultReader.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStreamDefaultReader.prototype.read, 'read');\nsetFunctionName(ReadableStreamDefaultReader.prototype.releaseLock, 'releaseLock');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamDefaultReader<R = any>(x: any): x is ReadableStreamDefaultReader<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultReader;\n}\n\nexport function ReadableStreamDefaultReaderRead<R>(reader: ReadableStreamDefaultReader<R>,\n                                                   readRequest: ReadRequest<R>): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    readRequest._closeSteps();\n  } else if (stream._state === 'errored') {\n    readRequest._errorSteps(stream._storedError);\n  } else {\n    assert(stream._state === 'readable');\n    stream._readableStreamController[PullSteps](readRequest as ReadRequest<any>);\n  }\n}\n\nexport function ReadableStreamDefaultReaderRelease(reader: ReadableStreamDefaultReader) {\n  ReadableStreamReaderGenericRelease(reader);\n  const e = new TypeError('Reader was released');\n  ReadableStreamDefaultReaderErrorReadRequests(reader, e);\n}\n\nexport function ReadableStreamDefaultReaderErrorReadRequests(reader: ReadableStreamDefaultReader, e: any) {\n  const readRequests = reader._readRequests;\n  reader._readRequests = new SimpleQueue();\n  readRequests.forEach(readRequest => {\n    readRequest._errorSteps(e);\n  });\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nfunction defaultReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultReader.prototype.${name} can only be used on a ReadableStreamDefaultReader`);\n}\n", "import { reflectCall } from 'lib/helpers/webidl';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport assert from '../../stub/assert';\n\ndeclare global {\n  interface ArrayBuffer {\n    readonly detached: boolean;\n\n    transfer(): ArrayBuffer;\n  }\n\n  function structuredClone<T>(value: T, options: { transfer: ArrayBuffer[] }): T;\n}\n\nexport function CreateArrayFromList<T extends any[]>(elements: T): T {\n  // We use arrays to represent lists, so this is basically a no-op.\n  // Do a slice though just in case we happen to depend on the unique-ness.\n  return elements.slice() as T;\n}\n\nexport function CopyDataBlockBytes(dest: ArrayBuffer,\n                                   destOffset: number,\n                                   src: ArrayBuffer,\n                                   srcOffset: number,\n                                   n: number) {\n  new Uint8Array(dest).set(new Uint8Array(src, srcOffset, n), destOffset);\n}\n\nexport let TransferArrayBuffer = (O: ArrayBuffer): ArrayBuffer => {\n  if (typeof O.transfer === 'function') {\n    TransferArrayBuffer = buffer => buffer.transfer();\n  } else if (typeof structuredClone === 'function') {\n    TransferArrayBuffer = buffer => structuredClone(buffer, { transfer: [buffer] });\n  } else {\n    // Not implemented correctly\n    TransferArrayBuffer = buffer => buffer;\n  }\n  return TransferArrayBuffer(O);\n};\n\nexport function CanTransferArrayBuffer(O: ArrayBuffer): boolean {\n  return !IsDetachedBuffer(O);\n}\n\nexport let IsDetachedBuffer = (O: ArrayBuffer): boolean => {\n  if (typeof O.detached === 'boolean') {\n    IsDetachedBuffer = buffer => buffer.detached;\n  } else {\n    // Not implemented correctly\n    IsDetachedBuffer = buffer => buffer.byteLength === 0;\n  }\n  return IsDetachedBuffer(O);\n};\n\nexport function ArrayBufferSlice(buffer: ArrayBuffer, begin: number, end: number): ArrayBuffer {\n  // ArrayBuffer.prototype.slice is not available on IE10\n  // https://www.caniuse.com/mdn-javascript_builtins_arraybuffer_slice\n  if (buffer.slice) {\n    return buffer.slice(begin, end);\n  }\n  const length = end - begin;\n  const slice = new ArrayBuffer(length);\n  CopyDataBlockBytes(slice, 0, buffer, begin, length);\n  return slice;\n}\n\nexport type MethodName<T> = {\n  [P in keyof T]: T[P] extends Function | undefined ? P : never;\n}[keyof T];\n\nexport function GetMethod<T, K extends MethodName<T>>(receiver: T, prop: K): T[K] | undefined {\n  const func = receiver[prop];\n  if (func === undefined || func === null) {\n    return undefined;\n  }\n  if (typeof func !== 'function') {\n    throw new TypeError(`${String(prop)} is not a function`);\n  }\n  return func;\n}\n\nexport interface SyncIteratorRecord<T> {\n  iterator: Iterator<T>,\n  nextMethod: Iterator<T>['next'],\n  done: boolean;\n}\n\nexport interface AsyncIteratorRecord<T> {\n  iterator: AsyncIterator<T>,\n  nextMethod: AsyncIterator<T>['next'],\n  done: boolean;\n}\n\nexport type SyncOrAsyncIteratorRecord<T> = SyncIteratorRecord<T> | AsyncIteratorRecord<T>;\n\nexport function CreateAsyncFromSyncIterator<T>(syncIteratorRecord: SyncIteratorRecord<T>): AsyncIteratorRecord<T> {\n  // Instead of re-implementing CreateAsyncFromSyncIterator and %AsyncFromSyncIteratorPrototype%,\n  // we use yield* inside an async generator function to achieve the same result.\n\n  // Wrap the sync iterator inside a sync iterable, so we can use it with yield*.\n  const syncIterable = {\n    [Symbol.iterator]: () => syncIteratorRecord.iterator\n  };\n  // Create an async generator function and immediately invoke it.\n  const asyncIterator = (async function* () {\n    return yield* syncIterable;\n  }());\n  // Return as an async iterator record.\n  const nextMethod = asyncIterator.next;\n  return { iterator: asyncIterator, nextMethod, done: false };\n}\n\n// Aligns with core-js/modules/es.symbol.async-iterator.js\nexport const SymbolAsyncIterator: (typeof Symbol)['asyncIterator'] =\n  Symbol.asyncIterator ??\n  Symbol.for?.('Symbol.asyncIterator') ??\n  '@@asyncIterator';\n\nexport type SyncOrAsyncIterable<T> = Iterable<T> | AsyncIterable<T>;\nexport type SyncOrAsyncIteratorMethod<T> = () => (Iterator<T> | AsyncIterator<T>);\n\nfunction GetIterator<T>(\n  obj: SyncOrAsyncIterable<T>,\n  hint: 'async',\n  method?: SyncOrAsyncIteratorMethod<T>\n): AsyncIteratorRecord<T>;\nfunction GetIterator<T>(\n  obj: Iterable<T>,\n  hint: 'sync',\n  method?: SyncOrAsyncIteratorMethod<T>\n): SyncIteratorRecord<T>;\nfunction GetIterator<T>(\n  obj: SyncOrAsyncIterable<T>,\n  hint = 'sync',\n  method?: SyncOrAsyncIteratorMethod<T>\n): SyncOrAsyncIteratorRecord<T> {\n  assert(hint === 'sync' || hint === 'async');\n  if (method === undefined) {\n    if (hint === 'async') {\n      method = GetMethod(obj as AsyncIterable<T>, SymbolAsyncIterator);\n      if (method === undefined) {\n        const syncMethod = GetMethod(obj as Iterable<T>, Symbol.iterator);\n        const syncIteratorRecord = GetIterator(obj as Iterable<T>, 'sync', syncMethod);\n        return CreateAsyncFromSyncIterator(syncIteratorRecord);\n      }\n    } else {\n      method = GetMethod(obj as Iterable<T>, Symbol.iterator);\n    }\n  }\n  if (method === undefined) {\n    throw new TypeError('The object is not iterable');\n  }\n  const iterator = reflectCall(method, obj, []);\n  if (!typeIsObject(iterator)) {\n    throw new TypeError('The iterator method must return an object');\n  }\n  const nextMethod = iterator.next;\n  return { iterator, nextMethod, done: false } as SyncOrAsyncIteratorRecord<T>;\n}\n\nexport { GetIterator };\n\nexport function IteratorNext<T>(iteratorRecord: AsyncIteratorRecord<T>): Promise<IteratorResult<T>> {\n  const result = reflectCall(iteratorRecord.nextMethod, iteratorRecord.iterator, []);\n  if (!typeIsObject(result)) {\n    throw new TypeError('The iterator.next() method must return an object');\n  }\n  return result;\n}\n\nexport function IteratorComplete<TReturn>(\n  iterResult: IteratorResult<unknown, TReturn>\n): iterResult is IteratorReturnResult<TReturn> {\n  assert(typeIsObject(iterResult));\n  return Boolean(iterResult.done);\n}\n\nexport function IteratorValue<T>(iterResult: IteratorYieldResult<T>): T {\n  assert(typeIsObject(iterResult));\n  return iterResult.value;\n}\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nimport { SymbolAsyncIterator } from '../../../lib/abstract-ops/ecmascript';\n\n// We cannot access %AsyncIteratorPrototype% without non-ES2018 syntax, but we can re-create it.\nexport const AsyncIteratorPrototype: AsyncIterable<any> = {\n  // ******** %AsyncIteratorPrototype% [ @@asyncIterator ] ( )\n  // https://tc39.github.io/ecma262/#sec-asynciteratorprototype-asynciterator\n  [SymbolAsyncIterator](this: AsyncIterator<any>) {\n    return this;\n  }\n};\nObject.defineProperty(AsyncIteratorPrototype, SymbolAsyncIterator, { enumerable: false });\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nimport { ReadableStream } from '../readable-stream';\nimport {\n  AcquireReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  type ReadableStreamDefaultReadResult,\n  type ReadRequest\n} from './default-reader';\nimport { ReadableStreamReaderGenericCancel, ReadableStreamReaderGenericRelease } from './generic-reader';\nimport assert from '../../stub/assert';\nimport { AsyncIteratorPrototype } from '@@target/stub/async-iterator-prototype';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  queueMicrotask,\n  transformPromiseWith\n} from '../helpers/webidl';\n\n/**\n * An async iterator returned by {@link ReadableStream.values}.\n *\n * @public\n */\nexport interface ReadableStreamAsyncIterator<R> extends AsyncIterableIterator<R> {\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nexport class ReadableStreamAsyncIteratorImpl<R> {\n  private readonly _reader: ReadableStreamDefaultReader<R>;\n  private readonly _preventCancel: boolean;\n  private _ongoingPromise: Promise<ReadableStreamDefaultReadResult<R>> | undefined = undefined;\n  private _isFinished = false;\n\n  constructor(reader: ReadableStreamDefaultReader<R>, preventCancel: boolean) {\n    this._reader = reader;\n    this._preventCancel = preventCancel;\n  }\n\n  next(): Promise<ReadableStreamDefaultReadResult<R>> {\n    const nextSteps = () => this._nextSteps();\n    this._ongoingPromise = this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, nextSteps, nextSteps) :\n      nextSteps();\n    return this._ongoingPromise;\n  }\n\n  return(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    const returnSteps = () => this._returnSteps(value);\n    return this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, returnSteps, returnSteps) :\n      returnSteps();\n  }\n\n  private _nextSteps(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value: undefined, done: true });\n    }\n\n    const reader = this._reader;\n    assert(reader._ownerReadableStream !== undefined);\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        this._ongoingPromise = undefined;\n        // This needs to be delayed by one microtask, otherwise we stop pulling too early which breaks a test.\n        // FIXME Is this a bug in the specification, or in the test?\n        queueMicrotask(() => resolvePromise({ value: chunk, done: false }));\n      },\n      _closeSteps: () => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        resolvePromise({ value: undefined, done: true });\n      },\n      _errorSteps: reason => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        rejectPromise(reason);\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n    return promise;\n  }\n\n  private _returnSteps(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value, done: true });\n    }\n    this._isFinished = true;\n\n    const reader = this._reader;\n    assert(reader._ownerReadableStream !== undefined);\n    assert(reader._readRequests.length === 0);\n\n    if (!this._preventCancel) {\n      const result = ReadableStreamReaderGenericCancel(reader, value);\n      ReadableStreamReaderGenericRelease(reader);\n      return transformPromiseWith(result, () => ({ value, done: true }));\n    }\n\n    ReadableStreamReaderGenericRelease(reader);\n    return promiseResolvedWith({ value, done: true });\n  }\n}\n\ninterface ReadableStreamAsyncIteratorInstance<R> extends ReadableStreamAsyncIterator<R> {\n  /** @interal */\n  _asyncIteratorImpl: ReadableStreamAsyncIteratorImpl<R>;\n\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nconst ReadableStreamAsyncIteratorPrototype: ReadableStreamAsyncIteratorInstance<any> = {\n  next(this: ReadableStreamAsyncIteratorInstance<any>): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('next'));\n    }\n    return this._asyncIteratorImpl.next();\n  },\n\n  return(this: ReadableStreamAsyncIteratorInstance<any>, value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('return'));\n    }\n    return this._asyncIteratorImpl.return(value);\n  }\n} as any;\nObject.setPrototypeOf(ReadableStreamAsyncIteratorPrototype, AsyncIteratorPrototype);\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamAsyncIterator<R>(stream: ReadableStream<R>,\n                                                      preventCancel: boolean): ReadableStreamAsyncIterator<R> {\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n  const impl = new ReadableStreamAsyncIteratorImpl(reader, preventCancel);\n  const iterator: ReadableStreamAsyncIteratorInstance<R> = Object.create(ReadableStreamAsyncIteratorPrototype);\n  iterator._asyncIteratorImpl = impl;\n  return iterator;\n}\n\nfunction IsReadableStreamAsyncIterator<R = any>(x: any): x is ReadableStreamAsyncIterator<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_asyncIteratorImpl')) {\n    return false;\n  }\n\n  try {\n    // noinspection SuspiciousTypeOfGuard\n    return (x as ReadableStreamAsyncIteratorInstance<any>)._asyncIteratorImpl instanceof\n      ReadableStreamAsyncIteratorImpl;\n  } catch {\n    return false;\n  }\n}\n\n// Helper functions for the ReadableStream.\n\nfunction streamAsyncIteratorBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStreamAsyncIterator.${name} can only be used on a ReadableSteamAsyncIterator`);\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isNaN#Polyfill\nconst NumberIsNaN: typeof Number.isNaN = Number.isNaN || function (x) {\n  // eslint-disable-next-line no-self-compare\n  return x !== x;\n};\n\nexport default NumberIsNaN;\n", "import NumberIsNaN from '../../stub/number-isnan';\nimport { ArrayBufferSlice } from './ecmascript';\nimport type { NonShared } from '../helpers/array-buffer-view';\n\nexport function IsNonNegativeNumber(v: number): boolean {\n  if (typeof v !== 'number') {\n    return false;\n  }\n\n  if (NumberIsNaN(v)) {\n    return false;\n  }\n\n  if (v < 0) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function CloneAsUint8Array(O: NonShared<ArrayBufferView>): NonShared<Uint8Array> {\n  const buffer = ArrayBufferSlice(O.buffer, O.byteOffset, O.byteOffset + O.byteLength);\n  return new Uint8Array(buffer) as NonShared<Uint8Array>;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsNonNegativeNumber } from './miscellaneous';\n\nexport interface QueueContainer<T> {\n  _queue: SimpleQueue<T>;\n  _queueTotalSize: number;\n}\n\nexport interface QueuePair<T> {\n  value: T;\n  size: number;\n}\n\nexport function DequeueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.shift()!;\n  container._queueTotalSize -= pair.size;\n  if (container._queueTotalSize < 0) {\n    container._queueTotalSize = 0;\n  }\n\n  return pair.value;\n}\n\nexport function EnqueueValueWithSize<T>(container: QueueContainer<QueuePair<T>>, value: T, size: number) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  if (!IsNonNegativeNumber(size) || size === Infinity) {\n    throw new RangeError('Size must be a finite, non-NaN, non-negative number.');\n  }\n\n  container._queue.push({ value, size });\n  container._queueTotalSize += size;\n}\n\nexport function PeekQueueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.peek();\n  return pair.value;\n}\n\nexport function ResetQueue<T>(container: QueueContainer<T>) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  container._queue = new SimpleQueue<T>();\n  container._queueTotalSize = 0;\n}\n", "export type TypedArray =\n  | Int8Array\n  | Uint8Array\n  | Uint8ClampedArray\n  | Int16Array\n  | Uint16Array\n  | Int32Array\n  | Uint32Array\n  | Float32Array\n  | Float64Array;\n\nexport type NonShared<T extends ArrayBufferView> = T & {\n  buffer: ArrayBuffer;\n}\n\nexport interface ArrayBufferViewConstructor<T extends ArrayBufferView = ArrayBufferView> {\n  new(buffer: ArrayBuffer, byteOffset: number, length?: number): T;\n\n  readonly prototype: T;\n}\n\nexport interface TypedArrayConstructor<T extends TypedArray = TypedArray> extends ArrayBufferViewConstructor<T> {\n  readonly BYTES_PER_ELEMENT: number;\n}\n\nexport type DataViewConstructor = ArrayBufferViewConstructor<DataView>;\n\nfunction isDataViewConstructor(ctor: Function): ctor is DataViewConstructor {\n  return ctor === DataView;\n}\n\nexport function isDataView(view: ArrayBufferView): view is DataView {\n  return isDataViewConstructor(view.constructor);\n}\n\nexport function arrayBufferViewElementSize<T extends ArrayBufferView>(ctor: ArrayBufferViewConstructor<T>): number {\n  if (isDataViewConstructor(ctor)) {\n    return 1;\n  }\n  return (ctor as unknown as TypedArrayConstructor).BYTES_PER_ELEMENT;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  IsReadableStreamDefaultReader,\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadableStreamHasDefaultReader,\n  type ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamAddReadIntoRequest,\n  ReadableStreamFulfillReadIntoRequest,\n  ReadableStreamGetNumReadIntoRequests,\n  ReadableStreamHasBYOBReader,\n  type ReadIntoRequest\n} from './byob-reader';\nimport NumberIsInteger from '../../stub/number-isinteger';\nimport {\n  IsReadableStreamLocked,\n  type ReadableByteStream,\n  ReadableStreamClose,\n  ReadableStreamError\n} from '../readable-stream';\nimport type { ValidatedUnderlyingByteSource } from './underlying-source';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport {\n  ArrayBufferSlice,\n  CanTransferArrayBuffer,\n  CopyDataBlockBytes,\n  IsDetachedBuffer,\n  TransferArrayBuffer\n} from '../abstract-ops/ecmascript';\nimport { CancelSteps, PullSteps, ReleaseSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\nimport { assertRequiredArgument, convertUnsignedLongLongWithEnforceRange } from '../validators/basic';\nimport {\n  type ArrayBufferViewConstructor,\n  arrayBufferViewElementSize,\n  type NonShared,\n  type TypedArrayConstructor\n} from '../helpers/array-buffer-view';\n\n/**\n * A pull-into request in a {@link ReadableByteStreamController}.\n *\n * @public\n */\nexport class ReadableStreamBYOBRequest {\n  /** @internal */\n  _associatedReadableByteStreamController!: ReadableByteStreamController;\n  /** @internal */\n  _view!: NonShared<ArrayBufferView> | null;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the view for writing in to, or `null` if the BYOB request has already been responded to.\n   */\n  get view(): ArrayBufferView | null {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('view');\n    }\n\n    return this._view;\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that `bytesWritten` bytes were written into\n   * {@link ReadableStreamBYOBRequest.view | view}, causing the result be surfaced to the consumer.\n   *\n   * After this method is called, {@link ReadableStreamBYOBRequest.view | view} will be transferred and no longer\n   * modifiable.\n   */\n  respond(bytesWritten: number): void;\n  respond(bytesWritten: number | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respond');\n    }\n    assertRequiredArgument(bytesWritten, 1, 'respond');\n    bytesWritten = convertUnsignedLongLongWithEnforceRange(bytesWritten, 'First parameter');\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(this._view!.buffer)) {\n      throw new TypeError(`The BYOB request's buffer has been detached and so cannot be used as a response`);\n    }\n\n    assert(this._view!.byteLength > 0);\n    assert(this._view!.buffer.byteLength > 0);\n\n    ReadableByteStreamControllerRespond(this._associatedReadableByteStreamController, bytesWritten);\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that instead of writing into\n   * {@link ReadableStreamBYOBRequest.view | view}, the underlying byte source is providing a new `ArrayBufferView`,\n   * which will be given to the consumer of the readable byte stream.\n   *\n   * After this method is called, `view` will be transferred and no longer modifiable.\n   */\n  respondWithNewView(view: ArrayBufferView): void;\n  respondWithNewView(view: NonShared<ArrayBufferView>): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respondWithNewView');\n    }\n    assertRequiredArgument(view, 1, 'respondWithNewView');\n\n    if (!ArrayBuffer.isView(view)) {\n      throw new TypeError('You can only respond with array buffer views');\n    }\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(view.buffer)) {\n      throw new TypeError('The given view\\'s buffer has been detached and so cannot be used as a response');\n    }\n\n    ReadableByteStreamControllerRespondWithNewView(this._associatedReadableByteStreamController, view);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBRequest.prototype, {\n  respond: { enumerable: true },\n  respondWithNewView: { enumerable: true },\n  view: { enumerable: true }\n});\nsetFunctionName(ReadableStreamBYOBRequest.prototype.respond, 'respond');\nsetFunctionName(ReadableStreamBYOBRequest.prototype.respondWithNewView, 'respondWithNewView');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBRequest.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBRequest',\n    configurable: true\n  });\n}\n\ninterface ByteQueueElement {\n  buffer: ArrayBuffer;\n  byteOffset: number;\n  byteLength: number;\n}\n\ntype PullIntoDescriptor<T extends NonShared<ArrayBufferView> = NonShared<ArrayBufferView>> =\n  DefaultPullIntoDescriptor\n  | BYOBPullIntoDescriptor<T>;\n\ninterface DefaultPullIntoDescriptor {\n  buffer: ArrayBuffer;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  minimumFill: number;\n  elementSize: number;\n  viewConstructor: TypedArrayConstructor<Uint8Array>;\n  readerType: 'default' | 'none';\n}\n\ninterface BYOBPullIntoDescriptor<T extends NonShared<ArrayBufferView> = NonShared<ArrayBufferView>> {\n  buffer: ArrayBuffer;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  minimumFill: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<T>;\n  readerType: 'byob' | 'none';\n}\n\n/**\n * Allows control of a {@link ReadableStream | readable byte stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableByteStreamController {\n  /** @internal */\n  _controlledReadableByteStream!: ReadableByteStream;\n  /** @internal */\n  _queue!: SimpleQueue<ByteQueueElement>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n  /** @internal */\n  _autoAllocateChunkSize: number | undefined;\n  /** @internal */\n  _byobRequest: ReadableStreamBYOBRequest | null;\n  /** @internal */\n  _pendingPullIntos!: SimpleQueue<PullIntoDescriptor>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the current BYOB pull request, or `null` if there isn't one.\n   */\n  get byobRequest(): ReadableStreamBYOBRequest | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('byobRequest');\n    }\n\n    return ReadableByteStreamControllerGetBYOBRequest(this);\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying byte source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableByteStreamControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('close');\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('The stream has already been closed; do not close it again!');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be closed`);\n    }\n\n    ReadableByteStreamControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk chunk in the controlled readable stream.\n   * The chunk has to be an `ArrayBufferView` instance, or else a `TypeError` will be thrown.\n   */\n  enqueue(chunk: ArrayBufferView): void;\n  enqueue(chunk: NonShared<ArrayBufferView>): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('enqueue');\n    }\n\n    assertRequiredArgument(chunk, 1, 'enqueue');\n    if (!ArrayBuffer.isView(chunk)) {\n      throw new TypeError('chunk must be an array buffer view');\n    }\n    if (chunk.byteLength === 0) {\n      throw new TypeError('chunk must have non-zero byteLength');\n    }\n    if (chunk.buffer.byteLength === 0) {\n      throw new TypeError(`chunk's buffer must have non-zero byteLength`);\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('stream is closed or draining');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be enqueued to`);\n    }\n\n    ReadableByteStreamControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('error');\n    }\n\n    ReadableByteStreamControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ReadableByteStreamControllerClearPendingPullIntos(this);\n\n    ResetQueue(this);\n\n    const result = this._cancelAlgorithm(reason);\n    ReadableByteStreamControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<NonShared<Uint8Array>>): void {\n    const stream = this._controlledReadableByteStream;\n    assert(ReadableStreamHasDefaultReader(stream));\n\n    if (this._queueTotalSize > 0) {\n      assert(ReadableStreamGetNumReadRequests(stream) === 0);\n\n      ReadableByteStreamControllerFillReadRequestFromQueue(this, readRequest);\n      return;\n    }\n\n    const autoAllocateChunkSize = this._autoAllocateChunkSize;\n    if (autoAllocateChunkSize !== undefined) {\n      let buffer: ArrayBuffer;\n      try {\n        buffer = new ArrayBuffer(autoAllocateChunkSize);\n      } catch (bufferE) {\n        readRequest._errorSteps(bufferE);\n        return;\n      }\n\n      const pullIntoDescriptor: DefaultPullIntoDescriptor = {\n        buffer,\n        bufferByteLength: autoAllocateChunkSize,\n        byteOffset: 0,\n        byteLength: autoAllocateChunkSize,\n        bytesFilled: 0,\n        minimumFill: 1,\n        elementSize: 1,\n        viewConstructor: Uint8Array,\n        readerType: 'default'\n      };\n\n      this._pendingPullIntos.push(pullIntoDescriptor);\n    }\n\n    ReadableStreamAddReadRequest(stream, readRequest);\n    ReadableByteStreamControllerCallPullIfNeeded(this);\n  }\n\n  /** @internal */\n  [ReleaseSteps](): void {\n    if (this._pendingPullIntos.length > 0) {\n      const firstPullInto = this._pendingPullIntos.peek();\n      firstPullInto.readerType = 'none';\n\n      this._pendingPullIntos = new SimpleQueue();\n      this._pendingPullIntos.push(firstPullInto);\n    }\n  }\n}\n\nObject.defineProperties(ReadableByteStreamController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  byobRequest: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(ReadableByteStreamController.prototype.close, 'close');\nsetFunctionName(ReadableByteStreamController.prototype.enqueue, 'enqueue');\nsetFunctionName(ReadableByteStreamController.prototype.error, 'error');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableByteStreamController.prototype, Symbol.toStringTag, {\n    value: 'ReadableByteStreamController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableByteStreamController.\n\nexport function IsReadableByteStreamController(x: any): x is ReadableByteStreamController {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableByteStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableByteStreamController;\n}\n\nfunction IsReadableStreamBYOBRequest(x: any): x is ReadableStreamBYOBRequest {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_associatedReadableByteStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBRequest;\n}\n\nfunction ReadableByteStreamControllerCallPullIfNeeded(controller: ReadableByteStreamController): void {\n  const shouldPull = ReadableByteStreamControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  // TODO: Test controller argument\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableByteStreamControllerCallPullIfNeeded(controller);\n      }\n\n      return null;\n    },\n    e => {\n      ReadableByteStreamControllerError(controller, e);\n      return null;\n    }\n  );\n}\n\nfunction ReadableByteStreamControllerClearPendingPullIntos(controller: ReadableByteStreamController) {\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n  controller._pendingPullIntos = new SimpleQueue();\n}\n\nfunction ReadableByteStreamControllerCommitPullIntoDescriptor<T extends NonShared<ArrayBufferView>>(\n  stream: ReadableByteStream,\n  pullIntoDescriptor: PullIntoDescriptor<T>\n) {\n  assert(stream._state !== 'errored');\n  assert(pullIntoDescriptor.readerType !== 'none');\n\n  let done = false;\n  if (stream._state === 'closed') {\n    assert(pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize === 0);\n    done = true;\n  }\n\n  const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n  if (pullIntoDescriptor.readerType === 'default') {\n    ReadableStreamFulfillReadRequest(stream, filledView as unknown as NonShared<Uint8Array>, done);\n  } else {\n    assert(pullIntoDescriptor.readerType === 'byob');\n    ReadableStreamFulfillReadIntoRequest(stream, filledView, done);\n  }\n}\n\nfunction ReadableByteStreamControllerConvertPullIntoDescriptor<T extends NonShared<ArrayBufferView>>(\n  pullIntoDescriptor: PullIntoDescriptor<T>\n): T {\n  const bytesFilled = pullIntoDescriptor.bytesFilled;\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  assert(bytesFilled <= pullIntoDescriptor.byteLength);\n  assert(bytesFilled % elementSize === 0);\n\n  return new pullIntoDescriptor.viewConstructor(\n    pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, bytesFilled / elementSize) as T;\n}\n\nfunction ReadableByteStreamControllerEnqueueChunkToQueue(controller: ReadableByteStreamController,\n                                                         buffer: ArrayBuffer,\n                                                         byteOffset: number,\n                                                         byteLength: number) {\n  controller._queue.push({ buffer, byteOffset, byteLength });\n  controller._queueTotalSize += byteLength;\n}\n\nfunction ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller: ReadableByteStreamController,\n                                                               buffer: ArrayBuffer,\n                                                               byteOffset: number,\n                                                               byteLength: number) {\n  let clonedChunk;\n  try {\n    clonedChunk = ArrayBufferSlice(buffer, byteOffset, byteOffset + byteLength);\n  } catch (cloneE) {\n    ReadableByteStreamControllerError(controller, cloneE);\n    throw cloneE;\n  }\n  ReadableByteStreamControllerEnqueueChunkToQueue(controller, clonedChunk, 0, byteLength);\n}\n\nfunction ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller: ReadableByteStreamController,\n                                                                    firstDescriptor: PullIntoDescriptor) {\n  assert(firstDescriptor.readerType === 'none');\n  if (firstDescriptor.bytesFilled > 0) {\n    ReadableByteStreamControllerEnqueueClonedChunkToQueue(\n      controller,\n      firstDescriptor.buffer,\n      firstDescriptor.byteOffset,\n      firstDescriptor.bytesFilled\n    );\n  }\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n}\n\nfunction ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller: ReadableByteStreamController,\n                                                                     pullIntoDescriptor: PullIntoDescriptor) {\n  const maxBytesToCopy = Math.min(controller._queueTotalSize,\n                                  pullIntoDescriptor.byteLength - pullIntoDescriptor.bytesFilled);\n  const maxBytesFilled = pullIntoDescriptor.bytesFilled + maxBytesToCopy;\n\n  let totalBytesToCopyRemaining = maxBytesToCopy;\n  let ready = false;\n  assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill);\n  const remainderBytes = maxBytesFilled % pullIntoDescriptor.elementSize;\n  const maxAlignedBytes = maxBytesFilled - remainderBytes;\n  // A descriptor for a read() request that is not yet filled up to its minimum length will stay at the head\n  // of the queue, so the underlying source can keep filling it.\n  if (maxAlignedBytes >= pullIntoDescriptor.minimumFill) {\n    totalBytesToCopyRemaining = maxAlignedBytes - pullIntoDescriptor.bytesFilled;\n    ready = true;\n  }\n\n  const queue = controller._queue;\n\n  while (totalBytesToCopyRemaining > 0) {\n    const headOfQueue = queue.peek();\n\n    const bytesToCopy = Math.min(totalBytesToCopyRemaining, headOfQueue.byteLength);\n\n    const destStart = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    CopyDataBlockBytes(pullIntoDescriptor.buffer, destStart, headOfQueue.buffer, headOfQueue.byteOffset, bytesToCopy);\n\n    if (headOfQueue.byteLength === bytesToCopy) {\n      queue.shift();\n    } else {\n      headOfQueue.byteOffset += bytesToCopy;\n      headOfQueue.byteLength -= bytesToCopy;\n    }\n    controller._queueTotalSize -= bytesToCopy;\n\n    ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesToCopy, pullIntoDescriptor);\n\n    totalBytesToCopyRemaining -= bytesToCopy;\n  }\n\n  if (!ready) {\n    assert(controller._queueTotalSize === 0);\n    assert(pullIntoDescriptor.bytesFilled > 0);\n    assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill);\n  }\n\n  return ready;\n}\n\nfunction ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller: ReadableByteStreamController,\n                                                                size: number,\n                                                                pullIntoDescriptor: PullIntoDescriptor) {\n  assert(controller._pendingPullIntos.length === 0 || controller._pendingPullIntos.peek() === pullIntoDescriptor);\n  assert(controller._byobRequest === null);\n  pullIntoDescriptor.bytesFilled += size;\n}\n\nfunction ReadableByteStreamControllerHandleQueueDrain(controller: ReadableByteStreamController) {\n  assert(controller._controlledReadableByteStream._state === 'readable');\n\n  if (controller._queueTotalSize === 0 && controller._closeRequested) {\n    ReadableByteStreamControllerClearAlgorithms(controller);\n    ReadableStreamClose(controller._controlledReadableByteStream);\n  } else {\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n  }\n}\n\nfunction ReadableByteStreamControllerInvalidateBYOBRequest(controller: ReadableByteStreamController) {\n  if (controller._byobRequest === null) {\n    return;\n  }\n\n  controller._byobRequest._associatedReadableByteStreamController = undefined!;\n  controller._byobRequest._view = null!;\n  controller._byobRequest = null;\n}\n\nfunction ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller: ReadableByteStreamController) {\n  assert(!controller._closeRequested);\n\n  while (controller._pendingPullIntos.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n\n    const pullIntoDescriptor = controller._pendingPullIntos.peek();\n    assert(pullIntoDescriptor.readerType !== 'none');\n\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n      ReadableByteStreamControllerCommitPullIntoDescriptor(\n        controller._controlledReadableByteStream,\n        pullIntoDescriptor\n      );\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller: ReadableByteStreamController) {\n  const reader = controller._controlledReadableByteStream._reader;\n  assert(IsReadableStreamDefaultReader(reader));\n  while (reader._readRequests.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n    const readRequest = reader._readRequests.shift();\n    ReadableByteStreamControllerFillReadRequestFromQueue(controller, readRequest);\n  }\n}\n\nexport function ReadableByteStreamControllerPullInto<T extends NonShared<ArrayBufferView>>(\n  controller: ReadableByteStreamController,\n  view: T,\n  min: number,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = controller._controlledReadableByteStream;\n\n  const ctor = view.constructor as ArrayBufferViewConstructor<T>;\n  const elementSize = arrayBufferViewElementSize(ctor);\n\n  const { byteOffset, byteLength } = view;\n\n  const minimumFill = min * elementSize;\n  assert(minimumFill >= elementSize && minimumFill <= byteLength);\n  assert(minimumFill % elementSize === 0);\n\n  let buffer: ArrayBuffer;\n  try {\n    buffer = TransferArrayBuffer(view.buffer);\n  } catch (e) {\n    readIntoRequest._errorSteps(e);\n    return;\n  }\n\n  const pullIntoDescriptor: BYOBPullIntoDescriptor<T> = {\n    buffer,\n    bufferByteLength: buffer.byteLength,\n    byteOffset,\n    byteLength,\n    bytesFilled: 0,\n    minimumFill,\n    elementSize,\n    viewConstructor: ctor,\n    readerType: 'byob'\n  };\n\n  if (controller._pendingPullIntos.length > 0) {\n    controller._pendingPullIntos.push(pullIntoDescriptor);\n\n    // No ReadableByteStreamControllerCallPullIfNeeded() call since:\n    // - No change happens on desiredSize\n    // - The source has already been notified of that there's at least 1 pending read(view)\n\n    ReadableStreamAddReadIntoRequest(stream, readIntoRequest);\n    return;\n  }\n\n  if (stream._state === 'closed') {\n    const emptyView = new ctor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, 0);\n    readIntoRequest._closeSteps(emptyView);\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n\n      ReadableByteStreamControllerHandleQueueDrain(controller);\n\n      readIntoRequest._chunkSteps(filledView);\n      return;\n    }\n\n    if (controller._closeRequested) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      readIntoRequest._errorSteps(e);\n      return;\n    }\n  }\n\n  controller._pendingPullIntos.push(pullIntoDescriptor);\n\n  ReadableStreamAddReadIntoRequest<T>(stream, readIntoRequest);\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInClosedState(controller: ReadableByteStreamController,\n                                                          firstDescriptor: PullIntoDescriptor) {\n  assert(firstDescriptor.bytesFilled % firstDescriptor.elementSize === 0);\n\n  if (firstDescriptor.readerType === 'none') {\n    ReadableByteStreamControllerShiftPendingPullInto(controller);\n  }\n\n  const stream = controller._controlledReadableByteStream;\n  if (ReadableStreamHasBYOBReader(stream)) {\n    while (ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n      const pullIntoDescriptor = ReadableByteStreamControllerShiftPendingPullInto(controller);\n      ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor);\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerRespondInReadableState(controller: ReadableByteStreamController,\n                                                            bytesWritten: number,\n                                                            pullIntoDescriptor: PullIntoDescriptor) {\n  assert(pullIntoDescriptor.bytesFilled + bytesWritten <= pullIntoDescriptor.byteLength);\n\n  ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesWritten, pullIntoDescriptor);\n\n  if (pullIntoDescriptor.readerType === 'none') {\n    ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, pullIntoDescriptor);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n    return;\n  }\n\n  if (pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill) {\n    // A descriptor for a read() request that is not yet filled up to its minimum length will stay at the head\n    // of the queue, so the underlying source can keep filling it.\n    return;\n  }\n\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n  const remainderSize = pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize;\n  if (remainderSize > 0) {\n    const end = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    ReadableByteStreamControllerEnqueueClonedChunkToQueue(\n      controller,\n      pullIntoDescriptor.buffer,\n      end - remainderSize,\n      remainderSize\n    );\n  }\n\n  pullIntoDescriptor.bytesFilled -= remainderSize;\n  ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor);\n\n  ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInternal(controller: ReadableByteStreamController, bytesWritten: number) {\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  assert(CanTransferArrayBuffer(firstDescriptor.buffer));\n\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n\n  const state = controller._controlledReadableByteStream._state;\n  if (state === 'closed') {\n    assert(bytesWritten === 0);\n    ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor);\n  } else {\n    assert(state === 'readable');\n    assert(bytesWritten > 0);\n    ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, firstDescriptor);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerShiftPendingPullInto(\n  controller: ReadableByteStreamController\n): PullIntoDescriptor {\n  assert(controller._byobRequest === null);\n  const descriptor = controller._pendingPullIntos.shift()!;\n  return descriptor;\n}\n\nfunction ReadableByteStreamControllerShouldCallPull(controller: ReadableByteStreamController): boolean {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return false;\n  }\n\n  if (controller._closeRequested) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (ReadableStreamHasDefaultReader(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  if (ReadableStreamHasBYOBReader(stream) && ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableByteStreamControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableByteStreamControllerClearAlgorithms(controller: ReadableByteStreamController) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\n// A client of ReadableByteStreamController may use these functions directly to bypass state check.\n\nexport function ReadableByteStreamControllerClose(controller: ReadableByteStreamController) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    controller._closeRequested = true;\n\n    return;\n  }\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (firstPendingPullInto.bytesFilled % firstPendingPullInto.elementSize !== 0) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      throw e;\n    }\n  }\n\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamClose(stream);\n}\n\nexport function ReadableByteStreamControllerEnqueue(\n  controller: ReadableByteStreamController,\n  chunk: NonShared<ArrayBufferView>\n) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  const { buffer, byteOffset, byteLength } = chunk;\n  if (IsDetachedBuffer(buffer)) {\n    throw new TypeError('chunk\\'s buffer is detached and so cannot be enqueued');\n  }\n  const transferredBuffer = TransferArrayBuffer(buffer);\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (IsDetachedBuffer(firstPendingPullInto.buffer)) {\n      throw new TypeError(\n        'The BYOB request\\'s buffer has been detached and so cannot be filled with an enqueued chunk'\n      );\n    }\n    ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n    firstPendingPullInto.buffer = TransferArrayBuffer(firstPendingPullInto.buffer);\n    if (firstPendingPullInto.readerType === 'none') {\n      ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, firstPendingPullInto);\n    }\n  }\n\n  if (ReadableStreamHasDefaultReader(stream)) {\n    ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller);\n    if (ReadableStreamGetNumReadRequests(stream) === 0) {\n      assert(controller._pendingPullIntos.length === 0);\n      ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    } else {\n      assert(controller._queue.length === 0);\n      if (controller._pendingPullIntos.length > 0) {\n        assert(controller._pendingPullIntos.peek().readerType === 'default');\n        ReadableByteStreamControllerShiftPendingPullInto(controller);\n      }\n      const transferredView = new Uint8Array(transferredBuffer, byteOffset, byteLength);\n      ReadableStreamFulfillReadRequest(stream, transferredView as NonShared<Uint8Array>, false);\n    }\n  } else if (ReadableStreamHasBYOBReader(stream)) {\n    // TODO: Ideally in this branch detaching should happen only if the buffer is not consumed fully.\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n  } else {\n    assert(!IsReadableStreamLocked(stream));\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableByteStreamControllerError(controller: ReadableByteStreamController, e: any) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ReadableByteStreamControllerClearPendingPullIntos(controller);\n\n  ResetQueue(controller);\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableByteStreamControllerFillReadRequestFromQueue(\n  controller: ReadableByteStreamController,\n  readRequest: ReadRequest<NonShared<Uint8Array>>\n) {\n  assert(controller._queueTotalSize > 0);\n\n  const entry = controller._queue.shift();\n  controller._queueTotalSize -= entry.byteLength;\n\n  ReadableByteStreamControllerHandleQueueDrain(controller);\n\n  const view = new Uint8Array(entry.buffer, entry.byteOffset, entry.byteLength);\n  readRequest._chunkSteps(view as NonShared<Uint8Array>);\n}\n\nexport function ReadableByteStreamControllerGetBYOBRequest(\n  controller: ReadableByteStreamController\n): ReadableStreamBYOBRequest | null {\n  if (controller._byobRequest === null && controller._pendingPullIntos.length > 0) {\n    const firstDescriptor = controller._pendingPullIntos.peek();\n    const view = new Uint8Array(firstDescriptor.buffer,\n                                firstDescriptor.byteOffset + firstDescriptor.bytesFilled,\n                                firstDescriptor.byteLength - firstDescriptor.bytesFilled);\n\n    const byobRequest: ReadableStreamBYOBRequest = Object.create(ReadableStreamBYOBRequest.prototype);\n    SetUpReadableStreamBYOBRequest(byobRequest, controller, view as NonShared<Uint8Array>);\n    controller._byobRequest = byobRequest;\n  }\n  return controller._byobRequest;\n}\n\nfunction ReadableByteStreamControllerGetDesiredSize(controller: ReadableByteStreamController): number | null {\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nexport function ReadableByteStreamControllerRespond(controller: ReadableByteStreamController, bytesWritten: number) {\n  assert(controller._pendingPullIntos.length > 0);\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (bytesWritten !== 0) {\n      throw new TypeError('bytesWritten must be 0 when calling respond() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (bytesWritten === 0) {\n      throw new TypeError('bytesWritten must be greater than 0 when calling respond() on a readable stream');\n    }\n    if (firstDescriptor.bytesFilled + bytesWritten > firstDescriptor.byteLength) {\n      throw new RangeError('bytesWritten out of range');\n    }\n  }\n\n  firstDescriptor.buffer = TransferArrayBuffer(firstDescriptor.buffer);\n\n  ReadableByteStreamControllerRespondInternal(controller, bytesWritten);\n}\n\nexport function ReadableByteStreamControllerRespondWithNewView(controller: ReadableByteStreamController,\n                                                               view: NonShared<ArrayBufferView>) {\n  assert(controller._pendingPullIntos.length > 0);\n  assert(!IsDetachedBuffer(view.buffer));\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (view.byteLength !== 0) {\n      throw new TypeError('The view\\'s length must be 0 when calling respondWithNewView() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (view.byteLength === 0) {\n      throw new TypeError(\n        'The view\\'s length must be greater than 0 when calling respondWithNewView() on a readable stream'\n      );\n    }\n  }\n\n  if (firstDescriptor.byteOffset + firstDescriptor.bytesFilled !== view.byteOffset) {\n    throw new RangeError('The region specified by view does not match byobRequest');\n  }\n  if (firstDescriptor.bufferByteLength !== view.buffer.byteLength) {\n    throw new RangeError('The buffer of view has different capacity than byobRequest');\n  }\n  if (firstDescriptor.bytesFilled + view.byteLength > firstDescriptor.byteLength) {\n    throw new RangeError('The region specified by view is larger than byobRequest');\n  }\n\n  const viewByteLength = view.byteLength;\n  firstDescriptor.buffer = TransferArrayBuffer(view.buffer);\n  ReadableByteStreamControllerRespondInternal(controller, viewByteLength);\n}\n\nexport function SetUpReadableByteStreamController(stream: ReadableByteStream,\n                                                  controller: ReadableByteStreamController,\n                                                  startAlgorithm: () => void | PromiseLike<void>,\n                                                  pullAlgorithm: () => Promise<void>,\n                                                  cancelAlgorithm: (reason: any) => Promise<void>,\n                                                  highWaterMark: number,\n                                                  autoAllocateChunkSize: number | undefined) {\n  assert(stream._readableStreamController === undefined);\n  if (autoAllocateChunkSize !== undefined) {\n    assert(NumberIsInteger(autoAllocateChunkSize));\n    assert(autoAllocateChunkSize > 0);\n  }\n\n  controller._controlledReadableByteStream = stream;\n\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._byobRequest = null;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._closeRequested = false;\n  controller._started = false;\n\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._autoAllocateChunkSize = autoAllocateChunkSize;\n\n  controller._pendingPullIntos = new SimpleQueue();\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableByteStreamControllerCallPullIfNeeded(controller);\n      return null;\n    },\n    r => {\n      ReadableByteStreamControllerError(controller, r);\n      return null;\n    }\n  );\n}\n\nexport function SetUpReadableByteStreamControllerFromUnderlyingSource(\n  stream: ReadableByteStream,\n  underlyingByteSource: ValidatedUnderlyingByteSource,\n  highWaterMark: number\n) {\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let pullAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingByteSource.start !== undefined) {\n    startAlgorithm = () => underlyingByteSource.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingByteSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingByteSource.pull!(controller);\n  } else {\n    pullAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingByteSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingByteSource.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  const autoAllocateChunkSize = underlyingByteSource.autoAllocateChunkSize;\n  if (autoAllocateChunkSize === 0) {\n    throw new TypeError('autoAllocateChunkSize must be greater than 0');\n  }\n\n  SetUpReadableByteStreamController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize\n  );\n}\n\nfunction SetUpReadableStreamBYOBRequest(request: ReadableStreamBYOBRequest,\n                                        controller: ReadableByteStreamController,\n                                        view: NonShared<ArrayBufferView>) {\n  assert(IsReadableByteStreamController(controller));\n  assert(typeof view === 'object');\n  assert(ArrayBuffer.isView(view));\n  assert(!IsDetachedBuffer(view.buffer));\n  request._associatedReadableByteStreamController = controller;\n  request._view = view;\n}\n\n// Helper functions for the ReadableStreamBYOBRequest.\n\nfunction byobRequestBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBRequest.prototype.${name} can only be used on a ReadableStreamBYOBRequest`);\n}\n\n// Helper functions for the ReadableByteStreamController.\n\nfunction byteStreamControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableByteStreamController.prototype.${name} can only be used on a ReadableByteStreamController`);\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, type ReadableByteStream, type ReadableStream } from '../readable-stream';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamController,\n  ReadableByteStreamControllerPullInto\n} from './byte-stream-controller';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\nimport { IsDetachedBuffer } from '../abstract-ops/ecmascript';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ValidatedReadableStreamBYOBReaderReadOptions\n} from './reader-options';\nimport { convertByobReadOptions } from '../validators/reader-options';\nimport { isDataView, type NonShared, type TypedArray } from '../helpers/array-buffer-view';\n\n/**\n * A result returned by {@link ReadableStreamBYOBReader.read}.\n *\n * @public\n */\nexport type ReadableStreamBYOBReadResult<T extends ArrayBufferView> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value: T | undefined;\n};\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamBYOBReader(stream: ReadableByteStream): ReadableStreamBYOBReader {\n  return new ReadableStreamBYOBReader(stream as ReadableStream<Uint8Array>);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadIntoRequest<T extends NonShared<ArrayBufferView>>(\n  stream: ReadableByteStream,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  assert(IsReadableStreamBYOBReader(stream._reader));\n  assert(stream._state === 'readable' || stream._state === 'closed');\n\n  (stream._reader! as ReadableStreamBYOBReader)._readIntoRequests.push(readIntoRequest);\n}\n\nexport function ReadableStreamFulfillReadIntoRequest(stream: ReadableByteStream,\n                                                     chunk: ArrayBufferView,\n                                                     done: boolean) {\n  const reader = stream._reader as ReadableStreamBYOBReader;\n\n  assert(reader._readIntoRequests.length > 0);\n\n  const readIntoRequest = reader._readIntoRequests.shift()!;\n  if (done) {\n    readIntoRequest._closeSteps(chunk);\n  } else {\n    readIntoRequest._chunkSteps(chunk);\n  }\n}\n\nexport function ReadableStreamGetNumReadIntoRequests(stream: ReadableByteStream): number {\n  return (stream._reader as ReadableStreamBYOBReader)._readIntoRequests.length;\n}\n\nexport function ReadableStreamHasBYOBReader(stream: ReadableByteStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamBYOBReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadIntoRequest<T extends NonShared<ArrayBufferView>> {\n  _chunkSteps(chunk: T): void;\n\n  _closeSteps(chunk: T | undefined): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A BYOB reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamBYOBReader {\n  /** @internal */\n  _ownerReadableStream!: ReadableByteStream;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readIntoRequests: SimpleQueue<ReadIntoRequest<any>>;\n\n  constructor(stream: ReadableStream<Uint8Array>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamBYOBReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    if (!IsReadableByteStreamController(stream._readableStreamController)) {\n      throw new TypeError('Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte ' +\n        'source');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readIntoRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Attempts to reads bytes into view, and returns a promise resolved with the result.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read<T extends ArrayBufferView>(\n    view: T,\n    options?: ReadableStreamBYOBReaderReadOptions\n  ): Promise<ReadableStreamBYOBReadResult<T>>;\n  read<T extends NonShared<ArrayBufferView>>(\n    view: T,\n    rawOptions: ReadableStreamBYOBReaderReadOptions | null | undefined = {}\n  ): Promise<ReadableStreamBYOBReadResult<T>> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('read'));\n    }\n\n    if (!ArrayBuffer.isView(view)) {\n      return promiseRejectedWith(new TypeError('view must be an array buffer view'));\n    }\n    if (view.byteLength === 0) {\n      return promiseRejectedWith(new TypeError('view must have non-zero byteLength'));\n    }\n    if (view.buffer.byteLength === 0) {\n      return promiseRejectedWith(new TypeError(`view's buffer must have non-zero byteLength`));\n    }\n    if (IsDetachedBuffer(view.buffer)) {\n      return promiseRejectedWith(new TypeError('view\\'s buffer has been detached'));\n    }\n\n    let options: ValidatedReadableStreamBYOBReaderReadOptions;\n    try {\n      options = convertByobReadOptions(rawOptions, 'options');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const min = options.min;\n    if (min === 0) {\n      return promiseRejectedWith(new TypeError('options.min must be greater than 0'));\n    }\n    if (!isDataView(view)) {\n      if (min > (view as unknown as TypedArray).length) {\n        return promiseRejectedWith(new RangeError('options.min must be less than or equal to view\\'s length'));\n      }\n    } else if (min > view.byteLength) {\n      return promiseRejectedWith(new RangeError('options.min must be less than or equal to view\\'s byteLength'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamBYOBReadResult<T>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamBYOBReadResult<T>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readIntoRequest: ReadIntoRequest<T> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: chunk => resolvePromise({ value: chunk, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamBYOBReaderRead(this, view, min, readIntoRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamBYOBReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamBYOBReader(this)) {\n      throw byobReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    ReadableStreamBYOBReaderRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nsetFunctionName(ReadableStreamBYOBReader.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStreamBYOBReader.prototype.read, 'read');\nsetFunctionName(ReadableStreamBYOBReader.prototype.releaseLock, 'releaseLock');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamBYOBReader(x: any): x is ReadableStreamBYOBReader {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readIntoRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBReader;\n}\n\nexport function ReadableStreamBYOBReaderRead<T extends NonShared<ArrayBufferView>>(\n  reader: ReadableStreamBYOBReader,\n  view: T,\n  min: number,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'errored') {\n    readIntoRequest._errorSteps(stream._storedError);\n  } else {\n    ReadableByteStreamControllerPullInto(\n      stream._readableStreamController as ReadableByteStreamController,\n      view,\n      min,\n      readIntoRequest\n    );\n  }\n}\n\nexport function ReadableStreamBYOBReaderRelease(reader: ReadableStreamBYOBReader) {\n  ReadableStreamReaderGenericRelease(reader);\n  const e = new TypeError('Reader was released');\n  ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e);\n}\n\nexport function ReadableStreamBYOBReaderErrorReadIntoRequests(reader: ReadableStreamBYOBReader, e: any) {\n  const readIntoRequests = reader._readIntoRequests;\n  reader._readIntoRequests = new SimpleQueue();\n  readIntoRequests.forEach(readIntoRequest => {\n    readIntoRequest._errorSteps(e);\n  });\n}\n\n// Helper functions for the ReadableStreamBYOBReader.\n\nfunction byobReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBReader.prototype.${name} can only be used on a ReadableStreamBYOBReader`);\n}\n", "import { assertDictionary, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ReadableStreamGetReaderOptions,\n  ValidatedReadableStreamBYOBReaderReadOptions\n} from '../readable-stream/reader-options';\n\nexport function convertReaderOptions(options: ReadableStreamGetReaderOptions | null | undefined,\n                                     context: string): ReadableStreamGetReaderOptions {\n  assertDictionary(options, context);\n  const mode = options?.mode;\n  return {\n    mode: mode === undefined ? undefined : convertReadableStreamReaderMode(mode, `${context} has member 'mode' that`)\n  };\n}\n\nfunction convertReadableStreamReaderMode(mode: string, context: string): 'byob' {\n  mode = `${mode}`;\n  if (mode !== 'byob') {\n    throw new TypeError(`${context} '${mode}' is not a valid enumeration value for ReadableStreamReaderMode`);\n  }\n  return mode;\n}\n\nexport function convertByobReadOptions(\n  options: ReadableStreamBYOBReaderReadOptions | null | undefined,\n  context: string\n): ValidatedReadableStreamBYOBReaderReadOptions {\n  assertDictionary(options, context);\n  const min = options?.min ?? 1;\n  return {\n    min: convertUnsignedLongLongWithEnforceRange(\n      min,\n      `${context} has member 'min' that`\n    )\n  };\n}\n", "import type { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport NumberIsNaN from '../../stub/number-isnan';\n\nexport function ExtractHighWaterMark(strategy: QueuingStrategy, defaultHWM: number): number {\n  const { highWaterMark } = strategy;\n\n  if (highWaterMark === undefined) {\n    return defaultHWM;\n  }\n\n  if (NumberIsNaN(highWaterMark) || highWaterMark < 0) {\n    throw new RangeError('Invalid highWaterMark');\n  }\n\n  return highWaterMark;\n}\n\nexport function ExtractSizeAlgorithm<T>(strategy: QueuingStrategy<T>): QueuingStrategySizeCallback<T> {\n  const { size } = strategy;\n\n  if (!size) {\n    return () => 1;\n  }\n\n  return size;\n}\n", "import type { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport { assertDictionary, assertFunction, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategy<T>(init: QueuingStrategy<T> | null | undefined,\n                                          context: string): QueuingStrategy<T> {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  const size = init?.size;\n  return {\n    highWaterMark: highWaterMark === undefined ? undefined : convertUnrestrictedDouble(highWaterMark),\n    size: size === undefined ? undefined : convertQueuingStrategySize(size, `${context} has member 'size' that`)\n  };\n}\n\nfunction convertQueuingStrategySize<T>(fn: QueuingStrategySizeCallback<T>,\n                                       context: string): QueuingStrategySizeCallback<T> {\n  assertFunction(fn, context);\n  return chunk => convertUnrestrictedDouble(fn(chunk));\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport type {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from '../writable-stream/underlying-sink';\nimport { WritableStreamDefaultController } from '../writable-stream';\n\nexport function convertUnderlyingSink<W>(original: UnderlyingSink<W> | null,\n                                         context: string): ValidatedUnderlyingSink<W> {\n  assertDictionary(original, context);\n  const abort = original?.abort;\n  const close = original?.close;\n  const start = original?.start;\n  const type = original?.type;\n  const write = original?.write;\n  return {\n    abort: abort === undefined ?\n      undefined :\n      convertUnderlyingSinkAbortCallback(abort, original!, `${context} has member 'abort' that`),\n    close: close === undefined ?\n      undefined :\n      convertUnderlyingSinkCloseCallback(close, original!, `${context} has member 'close' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSinkStartCallback(start, original!, `${context} has member 'start' that`),\n    write: write === undefined ?\n      undefined :\n      convertUnderlyingSinkWriteCallback(write, original!, `${context} has member 'write' that`),\n    type\n  };\n}\n\nfunction convertUnderlyingSinkAbortCallback(\n  fn: UnderlyingSinkAbortCallback,\n  original: UnderlyingSink,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSinkCloseCallback(\n  fn: UnderlyingSinkCloseCallback,\n  original: UnderlyingSink,\n  context: string\n): () => Promise<void> {\n  assertFunction(fn, context);\n  return () => promiseCall(fn, original, []);\n}\n\nfunction convertUnderlyingSinkStartCallback(\n  fn: UnderlyingSinkStartCallback,\n  original: UnderlyingSink,\n  context: string\n): UnderlyingSinkStartCallback {\n  assertFunction(fn, context);\n  return (controller: WritableStreamDefaultController) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSinkWriteCallback<W>(\n  fn: UnderlyingSinkWriteCallback<W>,\n  original: UnderlyingSink<W>,\n  context: string\n): (chunk: W, controller: WritableStreamDefaultController) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: W, controller: WritableStreamDefaultController) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import { IsWritableStream, WritableStream } from '../writable-stream';\n\nexport function assertWritableStream(x: unknown, context: string): asserts x is WritableStream {\n  if (!IsWritableStream(x)) {\n    throw new TypeError(`${context} is not a WritableStream.`);\n  }\n}\n", "/**\n * A signal object that allows you to communicate with a request and abort it if required\n * via its associated `AbortController` object.\n *\n * @remarks\n *   This interface is compatible with the `AbortSignal` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @public\n */\nexport interface AbortSignal {\n  /**\n   * Whether the request is aborted.\n   */\n  readonly aborted: boolean;\n\n  /**\n   * If aborted, returns the reason for aborting.\n   */\n  readonly reason?: any;\n\n  /**\n   * Add an event listener to be triggered when this signal becomes aborted.\n   */\n  addEventListener(type: 'abort', listener: () => void): void;\n\n  /**\n   * Remove an event listener that was previously added with {@link AbortSignal.addEventListener}.\n   */\n  removeEventListener(type: 'abort', listener: () => void): void;\n}\n\nexport function isAbortSignal(value: unknown): value is AbortSignal {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  try {\n    return typeof (value as AbortSignal).aborted === 'boolean';\n  } catch {\n    // AbortSignal.prototype.aborted throws if its brand check fails\n    return false;\n  }\n}\n\n/**\n * A controller object that allows you to abort an `AbortSignal` when desired.\n *\n * @remarks\n *   This interface is compatible with the `AbortController` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @internal\n */\nexport interface AbortController {\n  readonly signal: AbortSignal;\n\n  abort(reason?: any): void;\n}\n\ninterface AbortControllerConstructor {\n  new(): AbortController;\n}\n\nconst supportsAbortController = typeof (AbortController as any) === 'function';\n\n/**\n * Construct a new AbortController, if supported by the platform.\n *\n * @internal\n */\nexport function createAbortController(): AbortController | undefined {\n  if (supportsAbortController) {\n    return new (AbortController as AbortControllerConstructor)();\n  }\n  return undefined;\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponPromise\n} from './helpers/webidl';\nimport {\n  DequeueValue,\n  EnqueueValueWithSize,\n  PeekQueueValue,\n  type QueuePair,\n  ResetQueue\n} from './abstract-ops/queue-with-sizes';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { SimpleQueue } from './simple-queue';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { AbortSteps, ErrorSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport type {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from './writable-stream/underlying-sink';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertUnderlyingSink } from './validators/underlying-sink';\nimport { assertWritableStream } from './validators/writable-stream';\nimport { type AbortController, type AbortSignal, createAbortController } from './abort-signal';\n\ntype WritableStreamState = 'writable' | 'closed' | 'erroring' | 'errored';\n\ninterface WriteOrCloseRequest {\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n}\n\ntype WriteRequest = WriteOrCloseRequest;\ntype CloseRequest = WriteOrCloseRequest;\n\ninterface PendingAbortRequest {\n  _promise: Promise<undefined>;\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n  _reason: any;\n  _wasAlreadyErroring: boolean;\n}\n\n/**\n * A writable stream represents a destination for data, into which you can write.\n *\n * @public\n */\nclass WritableStream<W = any> {\n  /** @internal */\n  _state!: WritableStreamState;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _writer: WritableStreamDefaultWriter<W> | undefined;\n  /** @internal */\n  _writableStreamController!: WritableStreamDefaultController<W>;\n  /** @internal */\n  _writeRequests!: SimpleQueue<WriteRequest>;\n  /** @internal */\n  _inFlightWriteRequest: WriteRequest | undefined;\n  /** @internal */\n  _closeRequest: CloseRequest | undefined;\n  /** @internal */\n  _inFlightCloseRequest: CloseRequest | undefined;\n  /** @internal */\n  _pendingAbortRequest: PendingAbortRequest | undefined;\n  /** @internal */\n  _backpressure!: boolean;\n\n  constructor(underlyingSink?: UnderlyingSink<W>, strategy?: QueuingStrategy<W>);\n  constructor(rawUnderlyingSink: UnderlyingSink<W> | null | undefined = {},\n              rawStrategy: QueuingStrategy<W> | null | undefined = {}) {\n    if (rawUnderlyingSink === undefined) {\n      rawUnderlyingSink = null;\n    } else {\n      assertObject(rawUnderlyingSink, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSink = convertUnderlyingSink(rawUnderlyingSink, 'First parameter');\n\n    InitializeWritableStream(this);\n\n    const type = underlyingSink.type;\n    if (type !== undefined) {\n      throw new RangeError('Invalid type is specified');\n    }\n\n    const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n    const highWaterMark = ExtractHighWaterMark(strategy, 1);\n\n    SetUpWritableStreamDefaultControllerFromUnderlyingSink(this, underlyingSink, highWaterMark, sizeAlgorithm);\n  }\n\n  /**\n   * Returns whether or not the writable stream is locked to a writer.\n   */\n  get locked(): boolean {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsWritableStreamLocked(this);\n  }\n\n  /**\n   * Aborts the stream, signaling that the producer can no longer successfully write to the stream and it is to be\n   * immediately moved to an errored state, with any queued-up writes discarded. This will also execute any abort\n   * mechanism of the underlying sink.\n   *\n   * The returned promise will fulfill if the stream shuts down successfully, or reject if the underlying sink signaled\n   * that there was an error doing so. Additionally, it will reject with a `TypeError` (without attempting to cancel\n   * the stream) if the stream is currently locked.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('abort'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot abort a stream that already has a writer'));\n    }\n\n    return WritableStreamAbort(this, reason);\n  }\n\n  /**\n   * Closes the stream. The underlying sink will finish processing any previously-written chunks, before invoking its\n   * close behavior. During this time any further attempts to write will fail (without erroring the stream).\n   *\n   * The method returns a promise that will fulfill if all remaining chunks are successfully written and the stream\n   * successfully closes, or rejects if an error is encountered during this process. Additionally, it will reject with\n   * a `TypeError` (without attempting to cancel the stream) if the stream is currently locked.\n   */\n  close() {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('close'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close a stream that already has a writer'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamClose(this);\n  }\n\n  /**\n   * Creates a {@link WritableStreamDefaultWriter | writer} and locks the stream to the new writer. While the stream\n   * is locked, no other writer can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to write to a stream\n   * without interruption or interleaving. By getting a writer for the stream, you can ensure nobody else can write at\n   * the same time, which would cause the resulting written data to be unpredictable and probably useless.\n   */\n  getWriter(): WritableStreamDefaultWriter<W> {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('getWriter');\n    }\n\n    return AcquireWritableStreamDefaultWriter(this);\n  }\n}\n\nObject.defineProperties(WritableStream.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  getWriter: { enumerable: true },\n  locked: { enumerable: true }\n});\nsetFunctionName(WritableStream.prototype.abort, 'abort');\nsetFunctionName(WritableStream.prototype.close, 'close');\nsetFunctionName(WritableStream.prototype.getWriter, 'getWriter');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStream.prototype, Symbol.toStringTag, {\n    value: 'WritableStream',\n    configurable: true\n  });\n}\n\nexport {\n  AcquireWritableStreamDefaultWriter,\n  CreateWritableStream,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamDefaultControllerErrorIfNeeded,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite,\n  WritableStreamCloseQueuedOrInFlight\n};\n\nexport type {\n  UnderlyingSink,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkAbortCallback\n};\n\n// Abstract operations for the WritableStream.\n\nfunction AcquireWritableStreamDefaultWriter<W>(stream: WritableStream<W>): WritableStreamDefaultWriter<W> {\n  return new WritableStreamDefaultWriter(stream);\n}\n\n// Throws if and only if startAlgorithm throws.\nfunction CreateWritableStream<W>(startAlgorithm: () => void | PromiseLike<void>,\n                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                 closeAlgorithm: () => Promise<void>,\n                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                 highWaterMark = 1,\n                                 sizeAlgorithm: QueuingStrategySizeCallback<W> = () => 1) {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: WritableStream<W> = Object.create(WritableStream.prototype);\n  InitializeWritableStream(stream);\n\n  const controller: WritableStreamDefaultController<W> = Object.create(WritableStreamDefaultController.prototype);\n\n  SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm,\n                                       abortAlgorithm, highWaterMark, sizeAlgorithm);\n  return stream;\n}\n\nfunction InitializeWritableStream<W>(stream: WritableStream<W>) {\n  stream._state = 'writable';\n\n  // The error that will be reported by new method calls once the state becomes errored. Only set when [[state]] is\n  // 'erroring' or 'errored'. May be set to an undefined value.\n  stream._storedError = undefined;\n\n  stream._writer = undefined;\n\n  // Initialize to undefined first because the constructor of the controller checks this\n  // variable to validate the caller.\n  stream._writableStreamController = undefined!;\n\n  // This queue is placed here instead of the writer class in order to allow for passing a writer to the next data\n  // producer without waiting for the queued writes to finish.\n  stream._writeRequests = new SimpleQueue();\n\n  // Write requests are removed from _writeRequests when write() is called on the underlying sink. This prevents\n  // them from being erroneously rejected on error. If a write() call is in-flight, the request is stored here.\n  stream._inFlightWriteRequest = undefined;\n\n  // The promise that was returned from writer.close(). Stored here because it may be fulfilled after the writer\n  // has been detached.\n  stream._closeRequest = undefined;\n\n  // Close request is removed from _closeRequest when close() is called on the underlying sink. This prevents it\n  // from being erroneously rejected on error. If a close() call is in-flight, the request is stored here.\n  stream._inFlightCloseRequest = undefined;\n\n  // The promise that was returned from writer.abort(). This may also be fulfilled after the writer has detached.\n  stream._pendingAbortRequest = undefined;\n\n  // The backpressure signal set by the controller.\n  stream._backpressure = false;\n}\n\nfunction IsWritableStream(x: unknown): x is WritableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_writableStreamController')) {\n    return false;\n  }\n\n  return x instanceof WritableStream;\n}\n\nfunction IsWritableStreamLocked(stream: WritableStream): boolean {\n  assert(IsWritableStream(stream));\n\n  if (stream._writer === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamAbort(stream: WritableStream, reason: any): Promise<undefined> {\n  if (stream._state === 'closed' || stream._state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  stream._writableStreamController._abortReason = reason;\n  stream._writableStreamController._abortController?.abort(reason);\n\n  // TypeScript narrows the type of `stream._state` down to 'writable' | 'erroring',\n  // but it doesn't know that signaling abort runs author code that might have changed the state.\n  // Widen the type again by casting to WritableStreamState.\n  const state = stream._state as WritableStreamState;\n\n  if (state === 'closed' || state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._pendingAbortRequest !== undefined) {\n    return stream._pendingAbortRequest._promise;\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  let wasAlreadyErroring = false;\n  if (state === 'erroring') {\n    wasAlreadyErroring = true;\n    // reason will not be used, so don't keep a reference to it.\n    reason = undefined;\n  }\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    stream._pendingAbortRequest = {\n      _promise: undefined!,\n      _resolve: resolve,\n      _reject: reject,\n      _reason: reason,\n      _wasAlreadyErroring: wasAlreadyErroring\n    };\n  });\n  stream._pendingAbortRequest!._promise = promise;\n\n  if (!wasAlreadyErroring) {\n    WritableStreamStartErroring(stream, reason);\n  }\n\n  return promise;\n}\n\nfunction WritableStreamClose(stream: WritableStream<any>): Promise<undefined> {\n  const state = stream._state;\n  if (state === 'closed' || state === 'errored') {\n    return promiseRejectedWith(new TypeError(\n      `The stream (in ${state} state) is not in the writable state and cannot be closed`));\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const closeRequest: CloseRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._closeRequest = closeRequest;\n  });\n\n  const writer = stream._writer;\n  if (writer !== undefined && stream._backpressure && state === 'writable') {\n    defaultWriterReadyPromiseResolve(writer);\n  }\n\n  WritableStreamDefaultControllerClose(stream._writableStreamController);\n\n  return promise;\n}\n\n// WritableStream API exposed for controllers.\n\nfunction WritableStreamAddWriteRequest(stream: WritableStream): Promise<undefined> {\n  assert(IsWritableStreamLocked(stream));\n  assert(stream._state === 'writable');\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const writeRequest: WriteRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._writeRequests.push(writeRequest);\n  });\n\n  return promise;\n}\n\nfunction WritableStreamDealWithRejection(stream: WritableStream, error: any) {\n  const state = stream._state;\n\n  if (state === 'writable') {\n    WritableStreamStartErroring(stream, error);\n    return;\n  }\n\n  assert(state === 'erroring');\n  WritableStreamFinishErroring(stream);\n}\n\nfunction WritableStreamStartErroring(stream: WritableStream, reason: any) {\n  assert(stream._storedError === undefined);\n  assert(stream._state === 'writable');\n\n  const controller = stream._writableStreamController;\n  assert(controller !== undefined);\n\n  stream._state = 'erroring';\n  stream._storedError = reason;\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, reason);\n  }\n\n  if (!WritableStreamHasOperationMarkedInFlight(stream) && controller._started) {\n    WritableStreamFinishErroring(stream);\n  }\n}\n\nfunction WritableStreamFinishErroring(stream: WritableStream) {\n  assert(stream._state === 'erroring');\n  assert(!WritableStreamHasOperationMarkedInFlight(stream));\n  stream._state = 'errored';\n  stream._writableStreamController[ErrorSteps]();\n\n  const storedError = stream._storedError;\n  stream._writeRequests.forEach(writeRequest => {\n    writeRequest._reject(storedError);\n  });\n  stream._writeRequests = new SimpleQueue();\n\n  if (stream._pendingAbortRequest === undefined) {\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const abortRequest = stream._pendingAbortRequest;\n  stream._pendingAbortRequest = undefined;\n\n  if (abortRequest._wasAlreadyErroring) {\n    abortRequest._reject(storedError);\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const promise = stream._writableStreamController[AbortSteps](abortRequest._reason);\n  uponPromise(\n    promise,\n    () => {\n      abortRequest._resolve();\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n      return null;\n    },\n    (reason: any) => {\n      abortRequest._reject(reason);\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n      return null;\n    });\n}\n\nfunction WritableStreamFinishInFlightWrite(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._resolve(undefined);\n  stream._inFlightWriteRequest = undefined;\n}\n\nfunction WritableStreamFinishInFlightWriteWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._reject(error);\n  stream._inFlightWriteRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  WritableStreamDealWithRejection(stream, error);\n}\n\nfunction WritableStreamFinishInFlightClose(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._resolve(undefined);\n  stream._inFlightCloseRequest = undefined;\n\n  const state = stream._state;\n\n  assert(state === 'writable' || state === 'erroring');\n\n  if (state === 'erroring') {\n    // The error was too late to do anything, so it is ignored.\n    stream._storedError = undefined;\n    if (stream._pendingAbortRequest !== undefined) {\n      stream._pendingAbortRequest._resolve();\n      stream._pendingAbortRequest = undefined;\n    }\n  }\n\n  stream._state = 'closed';\n\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseResolve(writer);\n  }\n\n  assert(stream._pendingAbortRequest === undefined);\n  assert(stream._storedError === undefined);\n}\n\nfunction WritableStreamFinishInFlightCloseWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._reject(error);\n  stream._inFlightCloseRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  // Never execute sink abort() after sink close().\n  if (stream._pendingAbortRequest !== undefined) {\n    stream._pendingAbortRequest._reject(error);\n    stream._pendingAbortRequest = undefined;\n  }\n  WritableStreamDealWithRejection(stream, error);\n}\n\n// TODO(ricea): Fix alphabetical order.\nfunction WritableStreamCloseQueuedOrInFlight(stream: WritableStream): boolean {\n  if (stream._closeRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamHasOperationMarkedInFlight(stream: WritableStream): boolean {\n  if (stream._inFlightWriteRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamMarkCloseRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest === undefined);\n  assert(stream._closeRequest !== undefined);\n  stream._inFlightCloseRequest = stream._closeRequest;\n  stream._closeRequest = undefined;\n}\n\nfunction WritableStreamMarkFirstWriteRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest === undefined);\n  assert(stream._writeRequests.length !== 0);\n  stream._inFlightWriteRequest = stream._writeRequests.shift();\n}\n\nfunction WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream: WritableStream) {\n  assert(stream._state === 'errored');\n  if (stream._closeRequest !== undefined) {\n    assert(stream._inFlightCloseRequest === undefined);\n\n    stream._closeRequest._reject(stream._storedError);\n    stream._closeRequest = undefined;\n  }\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseReject(writer, stream._storedError);\n  }\n}\n\nfunction WritableStreamUpdateBackpressure(stream: WritableStream, backpressure: boolean) {\n  assert(stream._state === 'writable');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const writer = stream._writer;\n  if (writer !== undefined && backpressure !== stream._backpressure) {\n    if (backpressure) {\n      defaultWriterReadyPromiseReset(writer);\n    } else {\n      assert(!backpressure);\n\n      defaultWriterReadyPromiseResolve(writer);\n    }\n  }\n\n  stream._backpressure = backpressure;\n}\n\n/**\n * A default writer vended by a {@link WritableStream}.\n *\n * @public\n */\nexport class WritableStreamDefaultWriter<W = any> {\n  /** @internal */\n  _ownerWritableStream: WritableStream<W>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _closedPromiseState!: 'pending' | 'resolved' | 'rejected';\n  /** @internal */\n  _readyPromise!: Promise<undefined>;\n  /** @internal */\n  _readyPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _readyPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readyPromiseState!: 'pending' | 'fulfilled' | 'rejected';\n\n  constructor(stream: WritableStream<W>) {\n    assertRequiredArgument(stream, 1, 'WritableStreamDefaultWriter');\n    assertWritableStream(stream, 'First parameter');\n\n    if (IsWritableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive writing by another writer');\n    }\n\n    this._ownerWritableStream = stream;\n    stream._writer = this;\n\n    const state = stream._state;\n\n    if (state === 'writable') {\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._backpressure) {\n        defaultWriterReadyPromiseInitialize(this);\n      } else {\n        defaultWriterReadyPromiseInitializeAsResolved(this);\n      }\n\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'erroring') {\n      defaultWriterReadyPromiseInitializeAsRejected(this, stream._storedError);\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'closed') {\n      defaultWriterReadyPromiseInitializeAsResolved(this);\n      defaultWriterClosedPromiseInitializeAsResolved(this);\n    } else {\n      assert(state === 'errored');\n\n      const storedError = stream._storedError;\n      defaultWriterReadyPromiseInitializeAsRejected(this, storedError);\n      defaultWriterClosedPromiseInitializeAsRejected(this, storedError);\n    }\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the writer’s lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * Returns the desired size to fill the stream’s internal queue. It can be negative, if the queue is over-full.\n   * A producer can use this information to determine the right amount of data to write.\n   *\n   * It will be `null` if the stream cannot be successfully written to (due to either being errored, or having an abort\n   * queued up). It will return zero if the stream is closed. And the getter will throw an exception if invoked when\n   * the writer’s lock is released.\n   */\n  get desiredSize(): number | null {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('desiredSize');\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      throw defaultWriterLockException('desiredSize');\n    }\n\n    return WritableStreamDefaultWriterGetDesiredSize(this);\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the desired size to fill the stream’s internal queue transitions\n   * from non-positive to positive, signaling that it is no longer applying backpressure. Once the desired size dips\n   * back to zero or below, the getter will return a new promise that stays pending until the next transition.\n   *\n   * If the stream becomes errored or aborted, or the writer’s lock is released, the returned promise will become\n   * rejected.\n   */\n  get ready(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('ready'));\n    }\n\n    return this._readyPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.abort | stream.abort(reason)}.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('abort'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('abort'));\n    }\n\n    return WritableStreamDefaultWriterAbort(this, reason);\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.close | stream.close()}.\n   */\n  close(): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('close'));\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('close'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(stream)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamDefaultWriterClose(this);\n  }\n\n  /**\n   * Releases the writer’s lock on the corresponding stream. After the lock is released, the writer is no longer active.\n   * If the associated stream is errored when the lock is released, the writer will appear errored in the same way from\n   * now on; otherwise, the writer will appear closed.\n   *\n   * Note that the lock can still be released even if some ongoing writes have not yet finished (i.e. even if the\n   * promises returned from previous calls to {@link WritableStreamDefaultWriter.write | write()} have not yet settled).\n   * It’s not necessary to hold the lock on the writer for the duration of the write; the lock instead simply prevents\n   * other producers from writing in an interleaved manner.\n   */\n  releaseLock(): void {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('releaseLock');\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return;\n    }\n\n    assert(stream._writer !== undefined);\n\n    WritableStreamDefaultWriterRelease(this);\n  }\n\n  /**\n   * Writes the given chunk to the writable stream, by waiting until any previous writes have finished successfully,\n   * and then sending the chunk to the underlying sink's {@link UnderlyingSink.write | write()} method. It will return\n   * a promise that fulfills with undefined upon a successful write, or rejects if the write fails or stream becomes\n   * errored before the writing process is initiated.\n   *\n   * Note that what \"success\" means is up to the underlying sink; it might indicate simply that the chunk has been\n   * accepted, and not necessarily that it is safely saved to its ultimate destination.\n   */\n  write(chunk: W): Promise<void>;\n  write(chunk: W = undefined!): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('write'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('write to'));\n    }\n\n    return WritableStreamDefaultWriterWrite(this, chunk);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultWriter.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  releaseLock: { enumerable: true },\n  write: { enumerable: true },\n  closed: { enumerable: true },\n  desiredSize: { enumerable: true },\n  ready: { enumerable: true }\n});\nsetFunctionName(WritableStreamDefaultWriter.prototype.abort, 'abort');\nsetFunctionName(WritableStreamDefaultWriter.prototype.close, 'close');\nsetFunctionName(WritableStreamDefaultWriter.prototype.releaseLock, 'releaseLock');\nsetFunctionName(WritableStreamDefaultWriter.prototype.write, 'write');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultWriter.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultWriter',\n    configurable: true\n  });\n}\n\n// Abstract operations for the WritableStreamDefaultWriter.\n\nfunction IsWritableStreamDefaultWriter<W = any>(x: any): x is WritableStreamDefaultWriter<W> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_ownerWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultWriter;\n}\n\n// A client of WritableStreamDefaultWriter may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultWriterAbort(writer: WritableStreamDefaultWriter, reason: any) {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamAbort(stream, reason);\n}\n\nfunction WritableStreamDefaultWriterClose(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamClose(stream);\n}\n\nfunction WritableStreamDefaultWriterCloseWithErrorPropagation(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const state = stream._state;\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  return WritableStreamDefaultWriterClose(writer);\n}\n\nfunction WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._closedPromiseState === 'pending') {\n    defaultWriterClosedPromiseReject(writer, error);\n  } else {\n    defaultWriterClosedPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._readyPromiseState === 'pending') {\n    defaultWriterReadyPromiseReject(writer, error);\n  } else {\n    defaultWriterReadyPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterGetDesiredSize(writer: WritableStreamDefaultWriter): number | null {\n  const stream = writer._ownerWritableStream;\n  const state = stream._state;\n\n  if (state === 'errored' || state === 'erroring') {\n    return null;\n  }\n\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return WritableStreamDefaultControllerGetDesiredSize(stream._writableStreamController);\n}\n\nfunction WritableStreamDefaultWriterRelease(writer: WritableStreamDefaultWriter) {\n  const stream = writer._ownerWritableStream;\n  assert(stream !== undefined);\n  assert(stream._writer === writer);\n\n  const releasedError = new TypeError(\n    `Writer was released and can no longer be used to monitor the stream's closedness`);\n\n  WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, releasedError);\n\n  // The state transitions to \"errored\" before the sink abort() method runs, but the writer.closed promise is not\n  // rejected until afterwards. This means that simply testing state will not work.\n  WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, releasedError);\n\n  stream._writer = undefined;\n  writer._ownerWritableStream = undefined!;\n}\n\nfunction WritableStreamDefaultWriterWrite<W>(writer: WritableStreamDefaultWriter<W>, chunk: W): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const controller = stream._writableStreamController;\n\n  const chunkSize = WritableStreamDefaultControllerGetChunkSize(controller, chunk);\n\n  if (stream !== writer._ownerWritableStream) {\n    return promiseRejectedWith(defaultWriterLockException('write to'));\n  }\n\n  const state = stream._state;\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseRejectedWith(new TypeError('The stream is closing or closed and cannot be written to'));\n  }\n  if (state === 'erroring') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable');\n\n  const promise = WritableStreamAddWriteRequest(stream);\n\n  WritableStreamDefaultControllerWrite(controller, chunk, chunkSize);\n\n  return promise;\n}\n\nconst closeSentinel: unique symbol = {} as any;\n\ntype QueueRecord<W> = W | typeof closeSentinel;\n\n/**\n * Allows control of a {@link WritableStream | writable stream}'s state and internal queue.\n *\n * @public\n */\nexport class WritableStreamDefaultController<W = any> {\n  /** @internal */\n  _controlledWritableStream!: WritableStream<W>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<QueueRecord<W>>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _abortReason: any;\n  /** @internal */\n  _abortController: AbortController | undefined;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<W>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _writeAlgorithm!: (chunk: W) => Promise<void>;\n  /** @internal */\n  _closeAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _abortAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * The reason which was passed to `WritableStream.abort(reason)` when the stream was aborted.\n   *\n   * @deprecated\n   *  This property has been removed from the specification, see https://github.com/whatwg/streams/pull/1177.\n   *  Use {@link WritableStreamDefaultController.signal}'s `reason` instead.\n   */\n  get abortReason(): any {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('abortReason');\n    }\n    return this._abortReason;\n  }\n\n  /**\n   * An `AbortSignal` that can be used to abort the pending write or close operation when the stream is aborted.\n   */\n  get signal(): AbortSignal {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('signal');\n    }\n    if (this._abortController === undefined) {\n      // Older browsers or older Node versions may not support `AbortController` or `AbortSignal`.\n      // We don't want to bundle and ship an `AbortController` polyfill together with our polyfill,\n      // so instead we only implement support for `signal` if we find a global `AbortController` constructor.\n      throw new TypeError('WritableStreamDefaultController.prototype.signal is not supported');\n    }\n    return this._abortController.signal;\n  }\n\n  /**\n   * Closes the controlled writable stream, making all future interactions with it fail with the given error `e`.\n   *\n   * This method is rarely used, since usually it suffices to return a rejected promise from one of the underlying\n   * sink's methods. However, it can be useful for suddenly shutting down a stream in response to an event outside the\n   * normal lifecycle of interactions with the underlying sink.\n   */\n  error(e: any = undefined): void {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n    const state = this._controlledWritableStream._state;\n    if (state !== 'writable') {\n      // The stream is closed, errored or will be soon. The sink can't do anything useful if it gets an error here, so\n      // just treat it as a no-op.\n      return;\n    }\n\n    WritableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [AbortSteps](reason: any): Promise<void> {\n    const result = this._abortAlgorithm(reason);\n    WritableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [ErrorSteps]() {\n    ResetQueue(this);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultController.prototype, {\n  abortReason: { enumerable: true },\n  signal: { enumerable: true },\n  error: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations implementing interface required by the WritableStream.\n\nfunction IsWritableStreamDefaultController(x: any): x is WritableStreamDefaultController<any> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultController;\n}\n\nfunction SetUpWritableStreamDefaultController<W>(stream: WritableStream<W>,\n                                                 controller: WritableStreamDefaultController<W>,\n                                                 startAlgorithm: () => void | PromiseLike<void>,\n                                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                                 closeAlgorithm: () => Promise<void>,\n                                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                                 highWaterMark: number,\n                                                 sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  assert(IsWritableStream(stream));\n  assert(stream._writableStreamController === undefined);\n\n  controller._controlledWritableStream = stream;\n  stream._writableStreamController = controller;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._abortReason = undefined;\n  controller._abortController = createAbortController();\n  controller._started = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._writeAlgorithm = writeAlgorithm;\n  controller._closeAlgorithm = closeAlgorithm;\n  controller._abortAlgorithm = abortAlgorithm;\n\n  const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n  WritableStreamUpdateBackpressure(stream, backpressure);\n\n  const startResult = startAlgorithm();\n  const startPromise = promiseResolvedWith(startResult);\n  uponPromise(\n    startPromise,\n    () => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n      return null;\n    },\n    r => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDealWithRejection(stream, r);\n      return null;\n    }\n  );\n}\n\nfunction SetUpWritableStreamDefaultControllerFromUnderlyingSink<W>(stream: WritableStream<W>,\n                                                                   underlyingSink: ValidatedUnderlyingSink<W>,\n                                                                   highWaterMark: number,\n                                                                   sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  const controller = Object.create(WritableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let writeAlgorithm: (chunk: W) => Promise<void>;\n  let closeAlgorithm: () => Promise<void>;\n  let abortAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingSink.start !== undefined) {\n    startAlgorithm = () => underlyingSink.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingSink.write !== undefined) {\n    writeAlgorithm = chunk => underlyingSink.write!(chunk, controller);\n  } else {\n    writeAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSink.close !== undefined) {\n    closeAlgorithm = () => underlyingSink.close!();\n  } else {\n    closeAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSink.abort !== undefined) {\n    abortAlgorithm = reason => underlyingSink.abort!(reason);\n  } else {\n    abortAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpWritableStreamDefaultController(\n    stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// ClearAlgorithms may be called twice. Erroring the same stream in multiple ways will often result in redundant calls.\nfunction WritableStreamDefaultControllerClearAlgorithms(controller: WritableStreamDefaultController<any>) {\n  controller._writeAlgorithm = undefined!;\n  controller._closeAlgorithm = undefined!;\n  controller._abortAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\nfunction WritableStreamDefaultControllerClose<W>(controller: WritableStreamDefaultController<W>) {\n  EnqueueValueWithSize(controller, closeSentinel, 0);\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\nfunction WritableStreamDefaultControllerGetChunkSize<W>(controller: WritableStreamDefaultController<W>,\n                                                        chunk: W): number {\n  try {\n    return controller._strategySizeAlgorithm(chunk);\n  } catch (chunkSizeE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, chunkSizeE);\n    return 1;\n  }\n}\n\nfunction WritableStreamDefaultControllerGetDesiredSize(controller: WritableStreamDefaultController<any>): number {\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nfunction WritableStreamDefaultControllerWrite<W>(controller: WritableStreamDefaultController<W>,\n                                                 chunk: W,\n                                                 chunkSize: number) {\n  try {\n    EnqueueValueWithSize(controller, chunk, chunkSize);\n  } catch (enqueueE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, enqueueE);\n    return;\n  }\n\n  const stream = controller._controlledWritableStream;\n  if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._state === 'writable') {\n    const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n    WritableStreamUpdateBackpressure(stream, backpressure);\n  }\n\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\n// Abstract operations for the WritableStreamDefaultController.\n\nfunction WritableStreamDefaultControllerAdvanceQueueIfNeeded<W>(controller: WritableStreamDefaultController<W>) {\n  const stream = controller._controlledWritableStream;\n\n  if (!controller._started) {\n    return;\n  }\n\n  if (stream._inFlightWriteRequest !== undefined) {\n    return;\n  }\n\n  const state = stream._state;\n  assert(state !== 'closed' && state !== 'errored');\n  if (state === 'erroring') {\n    WritableStreamFinishErroring(stream);\n    return;\n  }\n\n  if (controller._queue.length === 0) {\n    return;\n  }\n\n  const value = PeekQueueValue(controller);\n  if (value === closeSentinel) {\n    WritableStreamDefaultControllerProcessClose(controller);\n  } else {\n    WritableStreamDefaultControllerProcessWrite(controller, value);\n  }\n}\n\nfunction WritableStreamDefaultControllerErrorIfNeeded(controller: WritableStreamDefaultController<any>, error: any) {\n  if (controller._controlledWritableStream._state === 'writable') {\n    WritableStreamDefaultControllerError(controller, error);\n  }\n}\n\nfunction WritableStreamDefaultControllerProcessClose(controller: WritableStreamDefaultController<any>) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkCloseRequestInFlight(stream);\n\n  DequeueValue(controller);\n  assert(controller._queue.length === 0);\n\n  const sinkClosePromise = controller._closeAlgorithm();\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  uponPromise(\n    sinkClosePromise,\n    () => {\n      WritableStreamFinishInFlightClose(stream);\n      return null;\n    },\n    reason => {\n      WritableStreamFinishInFlightCloseWithError(stream, reason);\n      return null;\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerProcessWrite<W>(controller: WritableStreamDefaultController<W>, chunk: W) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkFirstWriteRequestInFlight(stream);\n\n  const sinkWritePromise = controller._writeAlgorithm(chunk);\n  uponPromise(\n    sinkWritePromise,\n    () => {\n      WritableStreamFinishInFlightWrite(stream);\n\n      const state = stream._state;\n      assert(state === 'writable' || state === 'erroring');\n\n      DequeueValue(controller);\n\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && state === 'writable') {\n        const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n        WritableStreamUpdateBackpressure(stream, backpressure);\n      }\n\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n      return null;\n    },\n    reason => {\n      if (stream._state === 'writable') {\n        WritableStreamDefaultControllerClearAlgorithms(controller);\n      }\n      WritableStreamFinishInFlightWriteWithError(stream, reason);\n      return null;\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerGetBackpressure(controller: WritableStreamDefaultController<any>): boolean {\n  const desiredSize = WritableStreamDefaultControllerGetDesiredSize(controller);\n  return desiredSize <= 0;\n}\n\n// A client of WritableStreamDefaultController may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultControllerError(controller: WritableStreamDefaultController<any>, error: any) {\n  const stream = controller._controlledWritableStream;\n\n  assert(stream._state === 'writable');\n\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  WritableStreamStartErroring(stream, error);\n}\n\n// Helper functions for the WritableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`WritableStream.prototype.${name} can only be used on a WritableStream`);\n}\n\n// Helper functions for the WritableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultController.prototype.${name} can only be used on a WritableStreamDefaultController`);\n}\n\n\n// Helper functions for the WritableStreamDefaultWriter.\n\nfunction defaultWriterBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultWriter.prototype.${name} can only be used on a WritableStreamDefaultWriter`);\n}\n\nfunction defaultWriterLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released writer');\n}\n\nfunction defaultWriterClosedPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._closedPromise = newPromise((resolve, reject) => {\n    writer._closedPromise_resolve = resolve;\n    writer._closedPromise_reject = reject;\n    writer._closedPromiseState = 'pending';\n  });\n}\n\nfunction defaultWriterClosedPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseReject(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseResolve(writer);\n}\n\nfunction defaultWriterClosedPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._closedPromise_reject === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  setPromiseIsHandledToTrue(writer._closedPromise);\n  writer._closedPromise_reject(reason);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'rejected';\n}\n\nfunction defaultWriterClosedPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._closedPromise_resolve === undefined);\n  assert(writer._closedPromise_reject === undefined);\n  assert(writer._closedPromiseState !== 'pending');\n\n  defaultWriterClosedPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._closedPromise_resolve === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  writer._closedPromise_resolve(undefined);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'resolved';\n}\n\nfunction defaultWriterReadyPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._readyPromise = newPromise((resolve, reject) => {\n    writer._readyPromise_resolve = resolve;\n    writer._readyPromise_reject = reject;\n  });\n  writer._readyPromiseState = 'pending';\n}\n\nfunction defaultWriterReadyPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseReject(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseResolve(writer);\n}\n\nfunction defaultWriterReadyPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._readyPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(writer._readyPromise);\n  writer._readyPromise_reject(reason);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'rejected';\n}\n\nfunction defaultWriterReadyPromiseReset(writer: WritableStreamDefaultWriter) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitialize(writer);\n}\n\nfunction defaultWriterReadyPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._readyPromise_resolve === undefined) {\n    return;\n  }\n\n  writer._readyPromise_resolve(undefined);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'fulfilled';\n}\n", "/// <reference lib=\"dom\" />\n\nfunction getGlobals(): typeof globalThis | undefined {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis;\n  } else if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof global !== 'undefined') {\n    return global;\n  }\n  return undefined;\n}\n\nexport const globals = getGlobals();\n", "/// <reference types=\"node\" />\nimport { globals } from '../globals';\nimport { setFunctionName } from '../lib/helpers/miscellaneous';\n\ninterface DOMException extends Error {\n  name: string;\n  message: string;\n}\n\ntype DOMExceptionConstructor = new (message?: string, name?: string) => DOMException;\n\nfunction isDOMExceptionConstructor(ctor: unknown): ctor is DOMExceptionConstructor {\n  if (!(typeof ctor === 'function' || typeof ctor === 'object')) {\n    return false;\n  }\n  if ((ctor as DOMExceptionConstructor).name !== 'DOMException') {\n    return false;\n  }\n  try {\n    new (ctor as DOMExceptionConstructor)();\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Support:\n * - Web browsers\n * - Node 18 and higher (https://github.com/nodejs/node/commit/e4b1fb5e6422c1ff151234bb9de792d45dd88d87)\n */\nfunction getFromGlobal(): DOMExceptionConstructor | undefined {\n  const ctor = globals?.DOMException;\n  return isDOMExceptionConstructor(ctor) ? ctor : undefined;\n}\n\n/**\n * Support:\n * - All platforms\n */\nfunction createPolyfill(): DOMExceptionConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-shadow\n  const ctor = function DOMException(this: DOMException, message?: string, name?: string) {\n    this.message = message || '';\n    this.name = name || 'Error';\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  } as any;\n  setFunctionName(ctor, 'DOMException');\n  ctor.prototype = Object.create(Error.prototype);\n  Object.defineProperty(ctor.prototype, 'constructor', { value: ctor, writable: true, configurable: true });\n  return ctor;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nconst DOMException: DOMExceptionConstructor = getFromGlobal() || createPolyfill();\n\nexport { DOMException };\n", "import { IsReadableStream, IsReadableStreamLocked, ReadableStream, ReadableStreamCancel } from '../readable-stream';\nimport { AcquireReadableStreamDefaultReader, ReadableStreamDefaultReaderRead } from './default-reader';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireWritableStreamDefaultWriter,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamCloseQueuedOrInFlight,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite\n} from '../writable-stream';\nimport assert from '../../stub/assert';\nimport {\n  newPromise,\n  PerformPromiseThen,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponFulfillment,\n  uponPromise,\n  uponRejection\n} from '../helpers/webidl';\nimport { noop } from '../../utils';\nimport { type AbortSignal, isAbortSignal } from '../abort-signal';\nimport { DOMException } from '../../stub/dom-exception';\n\nexport function ReadableStreamPipeTo<T>(source: ReadableStream<T>,\n                                        dest: WritableStream<T>,\n                                        preventClose: boolean,\n                                        preventAbort: boolean,\n                                        preventCancel: boolean,\n                                        signal: AbortSignal | undefined): Promise<undefined> {\n  assert(IsReadableStream(source));\n  assert(IsWritableStream(dest));\n  assert(typeof preventClose === 'boolean');\n  assert(typeof preventAbort === 'boolean');\n  assert(typeof preventCancel === 'boolean');\n  assert(signal === undefined || isAbortSignal(signal));\n  assert(!IsReadableStreamLocked(source));\n  assert(!IsWritableStreamLocked(dest));\n\n  const reader = AcquireReadableStreamDefaultReader<T>(source);\n  const writer = AcquireWritableStreamDefaultWriter<T>(dest);\n\n  source._disturbed = true;\n\n  let shuttingDown = false;\n\n  // This is used to keep track of the spec's requirement that we wait for ongoing writes during shutdown.\n  let currentWrite = promiseResolvedWith<void>(undefined);\n\n  return newPromise((resolve, reject) => {\n    let abortAlgorithm: () => void;\n    if (signal !== undefined) {\n      abortAlgorithm = () => {\n        const error = signal.reason !== undefined ? signal.reason : new DOMException('Aborted', 'AbortError');\n        const actions: Array<() => Promise<void>> = [];\n        if (!preventAbort) {\n          actions.push(() => {\n            if (dest._state === 'writable') {\n              return WritableStreamAbort(dest, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        if (!preventCancel) {\n          actions.push(() => {\n            if (source._state === 'readable') {\n              return ReadableStreamCancel(source, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        shutdownWithAction(() => Promise.all(actions.map(action => action())), true, error);\n      };\n\n      if (signal.aborted) {\n        abortAlgorithm();\n        return;\n      }\n\n      signal.addEventListener('abort', abortAlgorithm);\n    }\n\n    // Using reader and writer, read all chunks from this and write them to dest\n    // - Backpressure must be enforced\n    // - Shutdown must stop all activity\n    function pipeLoop() {\n      return newPromise<void>((resolveLoop, rejectLoop) => {\n        function next(done: boolean) {\n          if (done) {\n            resolveLoop();\n          } else {\n            // Use `PerformPromiseThen` instead of `uponPromise` to avoid\n            // adding unnecessary `.catch(rethrowAssertionErrorRejection)` handlers\n            PerformPromiseThen(pipeStep(), next, rejectLoop);\n          }\n        }\n\n        next(false);\n      });\n    }\n\n    function pipeStep(): Promise<boolean> {\n      if (shuttingDown) {\n        return promiseResolvedWith(true);\n      }\n\n      return PerformPromiseThen(writer._readyPromise, () => {\n        return newPromise<boolean>((resolveRead, rejectRead) => {\n          ReadableStreamDefaultReaderRead(\n            reader,\n            {\n              _chunkSteps: chunk => {\n                currentWrite = PerformPromiseThen(WritableStreamDefaultWriterWrite(writer, chunk), undefined, noop);\n                resolveRead(false);\n              },\n              _closeSteps: () => resolveRead(true),\n              _errorSteps: rejectRead\n            }\n          );\n        });\n      });\n    }\n\n    // Errors must be propagated forward\n    isOrBecomesErrored(source, reader._closedPromise, storedError => {\n      if (!preventAbort) {\n        shutdownWithAction(() => WritableStreamAbort(dest, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n      return null;\n    });\n\n    // Errors must be propagated backward\n    isOrBecomesErrored(dest, writer._closedPromise, storedError => {\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n      return null;\n    });\n\n    // Closing must be propagated forward\n    isOrBecomesClosed(source, reader._closedPromise, () => {\n      if (!preventClose) {\n        shutdownWithAction(() => WritableStreamDefaultWriterCloseWithErrorPropagation(writer));\n      } else {\n        shutdown();\n      }\n      return null;\n    });\n\n    // Closing must be propagated backward\n    if (WritableStreamCloseQueuedOrInFlight(dest) || dest._state === 'closed') {\n      const destClosed = new TypeError('the destination writable stream closed before all data could be piped to it');\n\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, destClosed), true, destClosed);\n      } else {\n        shutdown(true, destClosed);\n      }\n    }\n\n    setPromiseIsHandledToTrue(pipeLoop());\n\n    function waitForWritesToFinish(): Promise<void> {\n      // Another write may have started while we were waiting on this currentWrite, so we have to be sure to wait\n      // for that too.\n      const oldCurrentWrite = currentWrite;\n      return PerformPromiseThen(\n        currentWrite,\n        () => oldCurrentWrite !== currentWrite ? waitForWritesToFinish() : undefined\n      );\n    }\n\n    function isOrBecomesErrored(stream: ReadableStream | WritableStream,\n                                promise: Promise<void>,\n                                action: (reason: any) => null) {\n      if (stream._state === 'errored') {\n        action(stream._storedError);\n      } else {\n        uponRejection(promise, action);\n      }\n    }\n\n    function isOrBecomesClosed(stream: ReadableStream | WritableStream, promise: Promise<void>, action: () => null) {\n      if (stream._state === 'closed') {\n        action();\n      } else {\n        uponFulfillment(promise, action);\n      }\n    }\n\n    function shutdownWithAction(action: () => Promise<unknown>, originalIsError?: boolean, originalError?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), doTheRest);\n      } else {\n        doTheRest();\n      }\n\n      function doTheRest(): null {\n        uponPromise(\n          action(),\n          () => finalize(originalIsError, originalError),\n          newError => finalize(true, newError)\n        );\n        return null;\n      }\n    }\n\n    function shutdown(isError?: boolean, error?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), () => finalize(isError, error));\n      } else {\n        finalize(isError, error);\n      }\n    }\n\n    function finalize(isError?: boolean, error?: any): null {\n      WritableStreamDefaultWriterRelease(writer);\n      ReadableStreamReaderGenericRelease(reader);\n\n      if (signal !== undefined) {\n        signal.removeEventListener('abort', abortAlgorithm);\n      }\n      if (isError) {\n        reject(error);\n      } else {\n        resolve(undefined);\n      }\n\n      return null;\n    }\n  });\n}\n", "import type { QueuingStrategySizeCallback } from '../queuing-strategy';\nimport assert from '../../stub/assert';\nimport { DequeueValue, EnqueueValueWithSize, type QueuePair, ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  type ReadRequest\n} from './default-reader';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsReadableStreamLocked, ReadableStream, ReadableStreamClose, ReadableStreamError } from '../readable-stream';\nimport type { ValidatedUnderlyingSource } from './underlying-source';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { CancelSteps, PullSteps, ReleaseSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\n\n/**\n * Allows control of a {@link ReadableStream | readable stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableStreamDefaultController<R> {\n  /** @internal */\n  _controlledReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<R>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<R>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableStreamDefaultControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('close');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits close');\n    }\n\n    ReadableStreamDefaultControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the controlled readable stream.\n   */\n  enqueue(chunk: R): void;\n  enqueue(chunk: R = undefined!): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits enqueue');\n    }\n\n    return ReadableStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    ReadableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ResetQueue(this);\n    const result = this._cancelAlgorithm(reason);\n    ReadableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<R>): void {\n    const stream = this._controlledReadableStream;\n\n    if (this._queue.length > 0) {\n      const chunk = DequeueValue(this);\n\n      if (this._closeRequested && this._queue.length === 0) {\n        ReadableStreamDefaultControllerClearAlgorithms(this);\n        ReadableStreamClose(stream);\n      } else {\n        ReadableStreamDefaultControllerCallPullIfNeeded(this);\n      }\n\n      readRequest._chunkSteps(chunk);\n    } else {\n      ReadableStreamAddReadRequest(stream, readRequest);\n      ReadableStreamDefaultControllerCallPullIfNeeded(this);\n    }\n  }\n\n  /** @internal */\n  [ReleaseSteps](): void {\n    // Do nothing.\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(ReadableStreamDefaultController.prototype.close, 'close');\nsetFunctionName(ReadableStreamDefaultController.prototype.enqueue, 'enqueue');\nsetFunctionName(ReadableStreamDefaultController.prototype.error, 'error');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableStreamDefaultController.\n\nfunction IsReadableStreamDefaultController<R = any>(x: any): x is ReadableStreamDefaultController<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultController;\n}\n\nfunction ReadableStreamDefaultControllerCallPullIfNeeded(controller: ReadableStreamDefaultController<any>): void {\n  const shouldPull = ReadableStreamDefaultControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      }\n\n      return null;\n    },\n    e => {\n      ReadableStreamDefaultControllerError(controller, e);\n      return null;\n    }\n  );\n}\n\nfunction ReadableStreamDefaultControllerShouldCallPull(controller: ReadableStreamDefaultController<any>): boolean {\n  const stream = controller._controlledReadableStream;\n\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableStreamDefaultControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableStreamDefaultControllerClearAlgorithms(controller: ReadableStreamDefaultController<any>) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\n// A client of ReadableStreamDefaultController may use these functions directly to bypass state check.\n\nexport function ReadableStreamDefaultControllerClose(controller: ReadableStreamDefaultController<any>) {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  controller._closeRequested = true;\n\n  if (controller._queue.length === 0) {\n    ReadableStreamDefaultControllerClearAlgorithms(controller);\n    ReadableStreamClose(stream);\n  }\n}\n\nexport function ReadableStreamDefaultControllerEnqueue<R>(\n  controller: ReadableStreamDefaultController<R>,\n  chunk: R\n): void {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    ReadableStreamFulfillReadRequest(stream, chunk, false);\n  } else {\n    let chunkSize;\n    try {\n      chunkSize = controller._strategySizeAlgorithm(chunk);\n    } catch (chunkSizeE) {\n      ReadableStreamDefaultControllerError(controller, chunkSizeE);\n      throw chunkSizeE;\n    }\n\n    try {\n      EnqueueValueWithSize(controller, chunk, chunkSize);\n    } catch (enqueueE) {\n      ReadableStreamDefaultControllerError(controller, enqueueE);\n      throw enqueueE;\n    }\n  }\n\n  ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableStreamDefaultControllerError(controller: ReadableStreamDefaultController<any>, e: any) {\n  const stream = controller._controlledReadableStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ResetQueue(controller);\n\n  ReadableStreamDefaultControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableStreamDefaultControllerGetDesiredSize(\n  controller: ReadableStreamDefaultController<any>\n): number | null {\n  const state = controller._controlledReadableStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\n// This is used in the implementation of TransformStream.\nexport function ReadableStreamDefaultControllerHasBackpressure(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  if (ReadableStreamDefaultControllerShouldCallPull(controller)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function ReadableStreamDefaultControllerCanCloseOrEnqueue(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  const state = controller._controlledReadableStream._state;\n\n  if (!controller._closeRequested && state === 'readable') {\n    return true;\n  }\n\n  return false;\n}\n\nexport function SetUpReadableStreamDefaultController<R>(stream: ReadableStream<R>,\n                                                        controller: ReadableStreamDefaultController<R>,\n                                                        startAlgorithm: () => void | PromiseLike<void>,\n                                                        pullAlgorithm: () => Promise<void>,\n                                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                                        highWaterMark: number,\n                                                        sizeAlgorithm: QueuingStrategySizeCallback<R>) {\n  assert(stream._readableStreamController === undefined);\n\n  controller._controlledReadableStream = stream;\n\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._started = false;\n  controller._closeRequested = false;\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      return null;\n    },\n    r => {\n      ReadableStreamDefaultControllerError(controller, r);\n      return null;\n    }\n  );\n}\n\nexport function SetUpReadableStreamDefaultControllerFromUnderlyingSource<R>(\n  stream: ReadableStream<R>,\n  underlyingSource: ValidatedUnderlyingSource<R>,\n  highWaterMark: number,\n  sizeAlgorithm: QueuingStrategySizeCallback<R>\n) {\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let pullAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingSource.start !== undefined) {\n    startAlgorithm = () => underlyingSource.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingSource.pull!(controller);\n  } else {\n    pullAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingSource.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// Helper functions for the ReadableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultController.prototype.${name} can only be used on a ReadableStreamDefaultController`);\n}\n", "import {\n  CreateReadableByteStream,\n  CreateReadableStream,\n  type DefaultReadableStream,\n  IsReadableStream,\n  type ReadableByteStream,\n  ReadableStream,\n  ReadableStreamCancel,\n  type ReadableStreamReader\n} from '../readable-stream';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  type ReadRequest\n} from './default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReaderRead,\n  type ReadIntoRequest\n} from './byob-reader';\nimport assert from '../../stub/assert';\nimport { newPromise, promiseResolvedWith, queueMicrotask, uponRejection } from '../helpers/webidl';\nimport {\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError\n} from './default-controller';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamControllerClose,\n  ReadableByteStreamControllerEnqueue,\n  ReadableByteStreamControllerError,\n  ReadableByteStreamControllerGetBYOBRequest,\n  ReadableByteStreamControllerRespond,\n  ReadableByteStreamControllerRespondWithNewView\n} from './byte-stream-controller';\nimport { CreateArrayFromList } from '../abstract-ops/ecmascript';\nimport { CloneAsUint8Array } from '../abstract-ops/miscellaneous';\nimport type { NonShared } from '../helpers/array-buffer-view';\n\nexport function ReadableStreamTee<R>(stream: ReadableStream<R>,\n                                     cloneForBranch2: boolean): [ReadableStream<R>, ReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n  if (IsReadableByteStreamController(stream._readableStreamController)) {\n    return ReadableByteStreamTee(stream as unknown as ReadableByteStream) as\n      unknown as [ReadableStream<R>, ReadableStream<R>];\n  }\n  return ReadableStreamDefaultTee(stream, cloneForBranch2);\n}\n\nexport function ReadableStreamDefaultTee<R>(\n  stream: ReadableStream<R>,\n  cloneForBranch2: boolean\n): [DefaultReadableStream<R>, DefaultReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n\n  let reading = false;\n  let readAgain = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: DefaultReadableStream<R>;\n  let branch2: DefaultReadableStream<R>;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<undefined>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function pullAlgorithm(): Promise<void> {\n    if (reading) {\n      readAgain = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgain = false;\n          const chunk1 = chunk;\n          const chunk2 = chunk;\n\n          // There is no way to access the cloning code right now in the reference implementation.\n          // If we add one then we'll need an implementation for serializable objects.\n          // if (!canceled2 && cloneForBranch2) {\n          //   chunk2 = StructuredDeserialize(StructuredSerialize(chunk2));\n          // }\n\n          if (!canceled1) {\n            ReadableStreamDefaultControllerEnqueue(branch1._readableStreamController, chunk1);\n          }\n          if (!canceled2) {\n            ReadableStreamDefaultControllerEnqueue(branch2._readableStreamController, chunk2);\n          }\n\n          reading = false;\n          if (readAgain) {\n            pullAlgorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableStreamDefaultControllerClose(branch1._readableStreamController);\n        }\n        if (!canceled2) {\n          ReadableStreamDefaultControllerClose(branch2._readableStreamController);\n        }\n\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm() {\n    // do nothing\n  }\n\n  branch1 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel1Algorithm);\n  branch2 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel2Algorithm);\n\n  uponRejection(reader._closedPromise, (r: any) => {\n    ReadableStreamDefaultControllerError(branch1._readableStreamController, r);\n    ReadableStreamDefaultControllerError(branch2._readableStreamController, r);\n    if (!canceled1 || !canceled2) {\n      resolveCancelPromise(undefined);\n    }\n    return null;\n  });\n\n  return [branch1, branch2];\n}\n\nexport function ReadableByteStreamTee(stream: ReadableByteStream): [ReadableByteStream, ReadableByteStream] {\n  assert(IsReadableStream(stream));\n  assert(IsReadableByteStreamController(stream._readableStreamController));\n\n  let reader: ReadableStreamReader<NonShared<Uint8Array>> = AcquireReadableStreamDefaultReader(stream);\n  let reading = false;\n  let readAgainForBranch1 = false;\n  let readAgainForBranch2 = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: ReadableByteStream;\n  let branch2: ReadableByteStream;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<void>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function forwardReaderError(thisReader: ReadableStreamReader<NonShared<Uint8Array>>) {\n    uponRejection(thisReader._closedPromise, r => {\n      if (thisReader !== reader) {\n        return null;\n      }\n      ReadableByteStreamControllerError(branch1._readableStreamController, r);\n      ReadableByteStreamControllerError(branch2._readableStreamController, r);\n      if (!canceled1 || !canceled2) {\n        resolveCancelPromise(undefined);\n      }\n      return null;\n    });\n  }\n\n  function pullWithDefaultReader() {\n    if (IsReadableStreamBYOBReader(reader)) {\n      assert(reader._readIntoRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamDefaultReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const readRequest: ReadRequest<NonShared<Uint8Array>> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const chunk1 = chunk;\n          let chunk2 = chunk;\n          if (!canceled1 && !canceled2) {\n            try {\n              chunk2 = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(branch1._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(branch2._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n          }\n\n          if (!canceled1) {\n            ReadableByteStreamControllerEnqueue(branch1._readableStreamController, chunk1);\n          }\n          if (!canceled2) {\n            ReadableByteStreamControllerEnqueue(branch2._readableStreamController, chunk2);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableByteStreamControllerClose(branch1._readableStreamController);\n        }\n        if (!canceled2) {\n          ReadableByteStreamControllerClose(branch2._readableStreamController);\n        }\n        if (branch1._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch1._readableStreamController, 0);\n        }\n        if (branch2._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch2._readableStreamController, 0);\n        }\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n  }\n\n  function pullWithBYOBReader(view: NonShared<ArrayBufferView>, forBranch2: boolean) {\n    if (IsReadableStreamDefaultReader<NonShared<Uint8Array>>(reader)) {\n      assert(reader._readRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamBYOBReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const byobBranch = forBranch2 ? branch2 : branch1;\n    const otherBranch = forBranch2 ? branch1 : branch2;\n\n    const readIntoRequest: ReadIntoRequest<NonShared<ArrayBufferView>> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const byobCanceled = forBranch2 ? canceled2 : canceled1;\n          const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n          if (!otherCanceled) {\n            let clonedChunk;\n            try {\n              clonedChunk = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(byobBranch._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(otherBranch._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n            if (!byobCanceled) {\n              ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n            }\n            ReadableByteStreamControllerEnqueue(otherBranch._readableStreamController, clonedChunk);\n          } else if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: chunk => {\n        reading = false;\n\n        const byobCanceled = forBranch2 ? canceled2 : canceled1;\n        const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n        if (!byobCanceled) {\n          ReadableByteStreamControllerClose(byobBranch._readableStreamController);\n        }\n        if (!otherCanceled) {\n          ReadableByteStreamControllerClose(otherBranch._readableStreamController);\n        }\n\n        if (chunk !== undefined) {\n          assert(chunk.byteLength === 0);\n\n          if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n          if (!otherCanceled && otherBranch._readableStreamController._pendingPullIntos.length > 0) {\n            ReadableByteStreamControllerRespond(otherBranch._readableStreamController, 0);\n          }\n        }\n\n        if (!byobCanceled || !otherCanceled) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamBYOBReaderRead(reader, view, 1, readIntoRequest);\n  }\n\n  function pull1Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch1 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch1._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, false);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function pull2Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch2 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch2._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, true);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm(): void {\n    return;\n  }\n\n  branch1 = CreateReadableByteStream(startAlgorithm, pull1Algorithm, cancel1Algorithm);\n  branch2 = CreateReadableByteStream(startAlgorithm, pull2Algorithm, cancel2Algorithm);\n\n  forwardReaderError(reader);\n\n  return [branch1, branch2];\n}\n", "import { CreateReadableStream, type DefaultReadableStream } from '../readable-stream';\nimport {\n  isReadableStreamLike,\n  type ReadableStreamDefaultReaderLike,\n  type ReadableStreamLike\n} from './readable-stream-like';\nimport { ReadableStreamDefaultControllerClose, ReadableStreamDefaultControllerEnqueue } from './default-controller';\nimport { GetIterator, GetMethod, IteratorComplete, IteratorNext, IteratorValue } from '../abstract-ops/ecmascript';\nimport { promiseRejectedWith, promiseResolvedWith, reflectCall, transformPromiseWith } from '../helpers/webidl';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { noop } from '../../utils';\n\nexport function ReadableStreamFrom<R>(\n  source: Iterable<R> | AsyncIterable<R> | ReadableStreamLike<R>\n): DefaultReadableStream<R> {\n  if (isReadableStreamLike(source)) {\n    return ReadableStreamFromDefaultReader(source.getReader());\n  }\n  return ReadableStreamFromIterable(source);\n}\n\nexport function ReadableStreamFromIterable<R>(asyncIterable: Iterable<R> | AsyncIterable<R>): DefaultReadableStream<R> {\n  let stream: DefaultReadableStream<R>;\n  const iteratorRecord = GetIterator(asyncIterable, 'async');\n\n  const startAlgorithm = noop;\n\n  function pullAlgorithm(): Promise<void> {\n    let nextResult;\n    try {\n      nextResult = IteratorNext(iteratorRecord);\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const nextPromise = promiseResolvedWith(nextResult);\n    return transformPromiseWith(nextPromise, iterResult => {\n      if (!typeIsObject(iterResult)) {\n        throw new TypeError('The promise returned by the iterator.next() method must fulfill with an object');\n      }\n      const done = IteratorComplete(iterResult);\n      if (done) {\n        ReadableStreamDefaultControllerClose(stream._readableStreamController);\n      } else {\n        const value = IteratorValue(iterResult);\n        ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);\n      }\n    });\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    const iterator = iteratorRecord.iterator;\n    let returnMethod: (typeof iterator)['return'] | undefined;\n    try {\n      returnMethod = GetMethod(iterator, 'return');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    if (returnMethod === undefined) {\n      return promiseResolvedWith(undefined);\n    }\n    let returnResult: IteratorResult<R> | Promise<IteratorResult<R>>;\n    try {\n      returnResult = reflectCall(returnMethod, iterator, [reason]);\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const returnPromise = promiseResolvedWith(returnResult);\n    return transformPromiseWith(returnPromise, iterResult => {\n      if (!typeIsObject(iterResult)) {\n        throw new TypeError('The promise returned by the iterator.return() method must fulfill with an object');\n      }\n      return undefined;\n    });\n  }\n\n  stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0);\n  return stream;\n}\n\nexport function ReadableStreamFromDefaultReader<R>(\n  reader: ReadableStreamDefaultReaderLike<R>\n): DefaultReadableStream<R> {\n  let stream: DefaultReadableStream<R>;\n\n  const startAlgorithm = noop;\n\n  function pullAlgorithm(): Promise<void> {\n    let readPromise;\n    try {\n      readPromise = reader.read();\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    return transformPromiseWith(readPromise, readResult => {\n      if (!typeIsObject(readResult)) {\n        throw new TypeError('The promise returned by the reader.read() method must fulfill with an object');\n      }\n      if (readResult.done) {\n        ReadableStreamDefaultControllerClose(stream._readableStreamController);\n      } else {\n        const value = readResult.value;\n        ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);\n      }\n    });\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    try {\n      return promiseResolvedWith(reader.cancel(reason));\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n  }\n\n  stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0);\n  return stream;\n}\n", "import { typeIsObject } from '../helpers/miscellaneous';\nimport type { ReadableStreamDefaultReadResult } from './default-reader';\n\n/**\n * A common interface for a `ReadadableStream` implementation.\n *\n * @public\n */\nexport interface ReadableStreamLike<R = any> {\n  readonly locked: boolean;\n\n  getReader(): ReadableStreamDefaultReaderLike<R>;\n}\n\n/**\n * A common interface for a `ReadableStreamDefaultReader` implementation.\n *\n * @public\n */\nexport interface ReadableStreamDefaultReaderLike<R = any> {\n  readonly closed: Promise<undefined>;\n\n  cancel(reason?: any): Promise<void>;\n\n  read(): Promise<ReadableStreamDefaultReadResult<R>>;\n\n  releaseLock(): void;\n}\n\nexport function isReadableStreamLike<R>(stream: unknown): stream is ReadableStreamLike<R> {\n  return typeIsObject(stream) && typeof (stream as ReadableStreamLike<R>).getReader !== 'undefined';\n}\n", "import { assertDictionary, assertFunction, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport type {\n  ReadableStreamController,\n  UnderlyingByteSource,\n  UnderlyingDefaultOrByteSource,\n  UnderlyingDefaultOrByteSourcePullCallback,\n  UnderlyingDefaultOrByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  ValidatedUnderlyingDefaultOrByteSource\n} from '../readable-stream/underlying-source';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\n\nexport function convertUnderlyingDefaultOrByteSource<R>(\n  source: UnderlyingSource<R> | UnderlyingByteSource | null,\n  context: string\n): ValidatedUnderlyingDefaultOrByteSource<R> {\n  assertDictionary(source, context);\n  const original = source as (UnderlyingDefaultOrByteSource<R> | null);\n  const autoAllocateChunkSize = original?.autoAllocateChunkSize;\n  const cancel = original?.cancel;\n  const pull = original?.pull;\n  const start = original?.start;\n  const type = original?.type;\n  return {\n    autoAllocateChunkSize: autoAllocateChunkSize === undefined ?\n      undefined :\n      convertUnsignedLongLongWithEnforceRange(\n        autoAllocateChunkSize,\n        `${context} has member 'autoAllocateChunkSize' that`\n      ),\n    cancel: cancel === undefined ?\n      undefined :\n      convertUnderlyingSourceCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    pull: pull === undefined ?\n      undefined :\n      convertUnderlyingSourcePullCallback(pull, original!, `${context} has member 'pull' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSourceStartCallback(start, original!, `${context} has member 'start' that`),\n    type: type === undefined ? undefined : convertReadableStreamType(type, `${context} has member 'type' that`)\n  };\n}\n\nfunction convertUnderlyingSourceCancelCallback(\n  fn: UnderlyingSourceCancelCallback,\n  original: UnderlyingDefaultOrByteSource,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSourcePullCallback<R>(\n  fn: UnderlyingDefaultOrByteSourcePullCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): (controller: ReadableStreamController<R>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSourceStartCallback<R>(\n  fn: UnderlyingDefaultOrByteSourceStartCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): UnderlyingDefaultOrByteSourceStartCallback<R> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertReadableStreamType(type: string, context: string): 'bytes' {\n  type = `${type}`;\n  if (type !== 'bytes') {\n    throw new TypeError(`${context} '${type}' is not a valid enumeration value for ReadableStreamType`);\n  }\n  return type;\n}\n", "import { assertDictionary } from './basic';\nimport type { StreamPipeOptions, ValidatedStreamPipeOptions } from '../readable-stream/pipe-options';\nimport { type AbortSignal, isAbortSignal } from '../abort-signal';\n\nexport function convertPipeOptions(options: StreamPipeOptions | null | undefined,\n                                   context: string): ValidatedStreamPipeOptions {\n  assertDictionary(options, context);\n  const preventAbort = options?.preventAbort;\n  const preventCancel = options?.preventCancel;\n  const preventClose = options?.preventClose;\n  const signal = options?.signal;\n  if (signal !== undefined) {\n    assertAbortSignal(signal, `${context} has member 'signal' that`);\n  }\n  return {\n    preventAbort: <PERSON><PERSON><PERSON>(preventAbort),\n    preventCancel: <PERSON><PERSON>an(preventCancel),\n    preventClose: Boolean(preventClose),\n    signal\n  };\n}\n\nfunction assertAbortSignal(signal: unknown, context: string): asserts signal is AbortSignal {\n  if (!isAbortSignal(signal)) {\n    throw new TypeError(`${context} is not an AbortSignal.`);\n  }\n}\n", "import assert from '../stub/assert';\nimport {\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith\n} from './helpers/webidl';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { AcquireReadableStreamAsyncIterator, type ReadableStreamAsyncIterator } from './readable-stream/async-iterator';\nimport { defaultReaderClosedPromiseReject, defaultReaderClosedPromiseResolve } from './readable-stream/generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderErrorReadRequests,\n  type ReadableStreamDefaultReadResult\n} from './readable-stream/default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBReaderErrorReadIntoRequests,\n  type ReadableStreamBYOBReadResult\n} from './readable-stream/byob-reader';\nimport { ReadableStreamPipeTo } from './readable-stream/pipe';\nimport { ReadableStreamTee } from './readable-stream/tee';\nimport { ReadableStreamFrom } from './readable-stream/from';\nimport { IsWritableStream, IsWritableStreamLocked, WritableStream } from './writable-stream';\nimport { SimpleQueue } from './simple-queue';\nimport {\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  SetUpReadableByteStreamController,\n  SetUpReadableByteStreamControllerFromUnderlyingSource\n} from './readable-stream/byte-stream-controller';\nimport {\n  ReadableStreamDefaultController,\n  SetUpReadableStreamDefaultController,\n  SetUpReadableStreamDefaultControllerFromUnderlyingSource\n} from './readable-stream/default-controller';\nimport type {\n  UnderlyingByteSource,\n  UnderlyingByteSourcePullCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceStartCallback\n} from './readable-stream/underlying-source';\nimport { noop } from '../utils';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { CreateArrayFromList, SymbolAsyncIterator } from './abstract-ops/ecmascript';\nimport { CancelSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertUnderlyingDefaultOrByteSource } from './validators/underlying-source';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ReadableStreamGetReaderOptions\n} from './readable-stream/reader-options';\nimport { convertReaderOptions } from './validators/reader-options';\nimport type { StreamPipeOptions, ValidatedStreamPipeOptions } from './readable-stream/pipe-options';\nimport type { ReadableStreamIteratorOptions } from './readable-stream/iterator-options';\nimport { convertIteratorOptions } from './validators/iterator-options';\nimport { convertPipeOptions } from './validators/pipe-options';\nimport type { ReadableWritablePair } from './readable-stream/readable-writable-pair';\nimport { convertReadableWritablePair } from './validators/readable-writable-pair';\nimport type { ReadableStreamDefaultReaderLike, ReadableStreamLike } from './readable-stream/readable-stream-like';\nimport type { NonShared } from './helpers/array-buffer-view';\n\nexport type DefaultReadableStream<R = any> = ReadableStream<R> & {\n  _readableStreamController: ReadableStreamDefaultController<R>\n};\n\nexport type ReadableByteStream = ReadableStream<NonShared<Uint8Array>> & {\n  _readableStreamController: ReadableByteStreamController\n};\n\ntype ReadableStreamState = 'readable' | 'closed' | 'errored';\n\n/**\n * A readable stream represents a source of data, from which you can read.\n *\n * @public\n */\nexport class ReadableStream<R = any> implements AsyncIterable<R> {\n  /** @internal */\n  _state!: ReadableStreamState;\n  /** @internal */\n  _reader: ReadableStreamReader<R> | undefined;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _disturbed!: boolean;\n  /** @internal */\n  _readableStreamController!: ReadableStreamDefaultController<R> | ReadableByteStreamController;\n\n  constructor(underlyingSource: UnderlyingByteSource, strategy?: { highWaterMark?: number; size?: undefined });\n  constructor(underlyingSource?: UnderlyingSource<R>, strategy?: QueuingStrategy<R>);\n  constructor(rawUnderlyingSource: UnderlyingSource<R> | UnderlyingByteSource | null | undefined = {},\n              rawStrategy: QueuingStrategy<R> | null | undefined = {}) {\n    if (rawUnderlyingSource === undefined) {\n      rawUnderlyingSource = null;\n    } else {\n      assertObject(rawUnderlyingSource, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSource = convertUnderlyingDefaultOrByteSource(rawUnderlyingSource, 'First parameter');\n\n    InitializeReadableStream(this);\n\n    if (underlyingSource.type === 'bytes') {\n      if (strategy.size !== undefined) {\n        throw new RangeError('The strategy for a byte stream cannot have a size function');\n      }\n      const highWaterMark = ExtractHighWaterMark(strategy, 0);\n      SetUpReadableByteStreamControllerFromUnderlyingSource(\n        this as unknown as ReadableByteStream,\n        underlyingSource,\n        highWaterMark\n      );\n    } else {\n      assert(underlyingSource.type === undefined);\n      const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n      const highWaterMark = ExtractHighWaterMark(strategy, 1);\n      SetUpReadableStreamDefaultControllerFromUnderlyingSource(\n        this,\n        underlyingSource,\n        highWaterMark,\n        sizeAlgorithm\n      );\n    }\n  }\n\n  /**\n   * Whether or not the readable stream is locked to a {@link ReadableStreamDefaultReader | reader}.\n   */\n  get locked(): boolean {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsReadableStreamLocked(this);\n  }\n\n  /**\n   * Cancels the stream, signaling a loss of interest in the stream by a consumer.\n   *\n   * The supplied `reason` argument will be given to the underlying source's {@link UnderlyingSource.cancel | cancel()}\n   * method, which might or might not use it.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('cancel'));\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot cancel a stream that already has a reader'));\n    }\n\n    return ReadableStreamCancel(this, reason);\n  }\n\n  /**\n   * Creates a {@link ReadableStreamBYOBReader} and locks the stream to the new reader.\n   *\n   * This call behaves the same way as the no-argument variant, except that it only works on readable byte streams,\n   * i.e. streams which were constructed specifically with the ability to handle \"bring your own buffer\" reading.\n   * The returned BYOB reader provides the ability to directly read individual chunks from the stream via its\n   * {@link ReadableStreamBYOBReader.read | read()} method, into developer-supplied buffers, allowing more precise\n   * control over allocation.\n   */\n  getReader({ mode }: { mode: 'byob' }): ReadableStreamBYOBReader;\n  /**\n   * Creates a {@link ReadableStreamDefaultReader} and locks the stream to the new reader.\n   * While the stream is locked, no other reader can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to consume a stream\n   * in its entirety. By getting a reader for the stream, you can ensure nobody else can interleave reads with yours\n   * or cancel the stream, which would interfere with your abstraction.\n   */\n  getReader(): ReadableStreamDefaultReader<R>;\n  getReader(\n    rawOptions: ReadableStreamGetReaderOptions | null | undefined = undefined\n  ): ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('getReader');\n    }\n\n    const options = convertReaderOptions(rawOptions, 'First parameter');\n\n    if (options.mode === undefined) {\n      return AcquireReadableStreamDefaultReader(this);\n    }\n\n    assert(options.mode === 'byob');\n    return AcquireReadableStreamBYOBReader(this as unknown as ReadableByteStream);\n  }\n\n  /**\n   * Provides a convenient, chainable way of piping this readable stream through a transform stream\n   * (or any other `{ writable, readable }` pair). It simply {@link ReadableStream.pipeTo | pipes} the stream\n   * into the writable side of the supplied pair, and returns the readable side for further use.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeThrough<RS extends ReadableStream>(\n    transform: { readable: RS; writable: WritableStream<R> },\n    options?: StreamPipeOptions\n  ): RS;\n  pipeThrough<RS extends ReadableStream>(\n    rawTransform: { readable: RS; writable: WritableStream<R> } | null | undefined,\n    rawOptions: StreamPipeOptions | null | undefined = {}\n  ): RS {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('pipeThrough');\n    }\n    assertRequiredArgument(rawTransform, 1, 'pipeThrough');\n\n    const transform = convertReadableWritablePair(rawTransform, 'First parameter');\n    const options = convertPipeOptions(rawOptions, 'Second parameter');\n\n    if (IsReadableStreamLocked(this)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream');\n    }\n    if (IsWritableStreamLocked(transform.writable)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream');\n    }\n\n    const promise = ReadableStreamPipeTo(\n      this, transform.writable, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n\n    setPromiseIsHandledToTrue(promise);\n\n    return transform.readable;\n  }\n\n  /**\n   * Pipes this readable stream to a given writable stream. The way in which the piping process behaves under\n   * various error conditions can be customized with a number of passed options. It returns a promise that fulfills\n   * when the piping process completes successfully, or rejects if any errors were encountered.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeTo(destination: WritableStream<R>, options?: StreamPipeOptions): Promise<void>;\n  pipeTo(destination: WritableStream<R> | null | undefined,\n         rawOptions: StreamPipeOptions | null | undefined = {}): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('pipeTo'));\n    }\n\n    if (destination === undefined) {\n      return promiseRejectedWith(`Parameter 1 is required in 'pipeTo'.`);\n    }\n    if (!IsWritableStream(destination)) {\n      return promiseRejectedWith(\n        new TypeError(`ReadableStream.prototype.pipeTo's first argument must be a WritableStream`)\n      );\n    }\n\n    let options: ValidatedStreamPipeOptions;\n    try {\n      options = convertPipeOptions(rawOptions, 'Second parameter');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream')\n      );\n    }\n    if (IsWritableStreamLocked(destination)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream')\n      );\n    }\n\n    return ReadableStreamPipeTo<R>(\n      this, destination, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n  }\n\n  /**\n   * Tees this readable stream, returning a two-element array containing the two resulting branches as\n   * new {@link ReadableStream} instances.\n   *\n   * Teeing a stream will lock it, preventing any other consumer from acquiring a reader.\n   * To cancel the stream, cancel both of the resulting branches; a composite cancellation reason will then be\n   * propagated to the stream's underlying source.\n   *\n   * Note that the chunks seen in each branch will be the same object. If the chunks are not immutable,\n   * this could allow interference between the two branches.\n   */\n  tee(): [ReadableStream<R>, ReadableStream<R>] {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('tee');\n    }\n\n    const branches = ReadableStreamTee(this, false);\n    return CreateArrayFromList(branches);\n  }\n\n  /**\n   * Asynchronously iterates over the chunks in the stream's internal queue.\n   *\n   * Asynchronously iterating over the stream will lock it, preventing any other consumer from acquiring a reader.\n   * The lock will be released if the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method\n   * is called, e.g. by breaking out of the loop.\n   *\n   * By default, calling the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method will also\n   * cancel the stream. To prevent this, use the stream's {@link ReadableStream.values | values()} method, passing\n   * `true` for the `preventCancel` option.\n   */\n  values(options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n  values(rawOptions: ReadableStreamIteratorOptions | null | undefined = undefined): ReadableStreamAsyncIterator<R> {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('values');\n    }\n\n    const options = convertIteratorOptions(rawOptions, 'First parameter');\n    return AcquireReadableStreamAsyncIterator<R>(this, options.preventCancel);\n  }\n\n  /**\n   * {@inheritDoc ReadableStream.values}\n   */\n  [Symbol.asyncIterator](options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n\n  [SymbolAsyncIterator](options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R> {\n    // Stub implementation, overridden below\n    return this.values(options);\n  }\n\n  /**\n   * Creates a new ReadableStream wrapping the provided iterable or async iterable.\n   *\n   * This can be used to adapt various kinds of objects into a readable stream,\n   * such as an array, an async generator, or a Node.js readable stream.\n   */\n  static from<R>(asyncIterable: Iterable<R> | AsyncIterable<R> | ReadableStreamLike<R>): ReadableStream<R> {\n    return ReadableStreamFrom(asyncIterable);\n  }\n}\n\nObject.defineProperties(ReadableStream, {\n  from: { enumerable: true }\n});\nObject.defineProperties(ReadableStream.prototype, {\n  cancel: { enumerable: true },\n  getReader: { enumerable: true },\n  pipeThrough: { enumerable: true },\n  pipeTo: { enumerable: true },\n  tee: { enumerable: true },\n  values: { enumerable: true },\n  locked: { enumerable: true }\n});\nsetFunctionName(ReadableStream.from, 'from');\nsetFunctionName(ReadableStream.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStream.prototype.getReader, 'getReader');\nsetFunctionName(ReadableStream.prototype.pipeThrough, 'pipeThrough');\nsetFunctionName(ReadableStream.prototype.pipeTo, 'pipeTo');\nsetFunctionName(ReadableStream.prototype.tee, 'tee');\nsetFunctionName(ReadableStream.prototype.values, 'values');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.toStringTag, {\n    value: 'ReadableStream',\n    configurable: true\n  });\n}\nObject.defineProperty(ReadableStream.prototype, SymbolAsyncIterator, {\n  value: ReadableStream.prototype.values,\n  writable: true,\n  configurable: true\n});\n\nexport type {\n  ReadableStreamAsyncIterator,\n  ReadableStreamDefaultReadResult,\n  ReadableStreamBYOBReadResult,\n  ReadableStreamBYOBReaderReadOptions,\n  UnderlyingByteSource,\n  UnderlyingSource,\n  UnderlyingSourceStartCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceCancelCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingByteSourcePullCallback,\n  StreamPipeOptions,\n  ReadableWritablePair,\n  ReadableStreamIteratorOptions,\n  ReadableStreamLike,\n  ReadableStreamDefaultReaderLike\n};\n\n// Abstract operations for the ReadableStream.\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableStream<R>(\n  startAlgorithm: () => void | PromiseLike<void>,\n  pullAlgorithm: () => Promise<void>,\n  cancelAlgorithm: (reason: any) => Promise<void>,\n  highWaterMark = 1,\n  sizeAlgorithm: QueuingStrategySizeCallback<R> = () => 1\n): DefaultReadableStream<R> {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: DefaultReadableStream<R> = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n\n  return stream;\n}\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableByteStream(\n  startAlgorithm: () => void | PromiseLike<void>,\n  pullAlgorithm: () => Promise<void>,\n  cancelAlgorithm: (reason: any) => Promise<void>\n): ReadableByteStream {\n  const stream: ReadableByteStream = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n  SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, 0, undefined);\n\n  return stream;\n}\n\nfunction InitializeReadableStream(stream: ReadableStream) {\n  stream._state = 'readable';\n  stream._reader = undefined;\n  stream._storedError = undefined;\n  stream._disturbed = false;\n}\n\nexport function IsReadableStream(x: unknown): x is ReadableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readableStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStream;\n}\n\nexport function IsReadableStreamDisturbed(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  return stream._disturbed;\n}\n\nexport function IsReadableStreamLocked(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  if (stream._reader === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamCancel<R>(stream: ReadableStream<R>, reason: any): Promise<undefined> {\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  ReadableStreamClose(stream);\n\n  const reader = stream._reader;\n  if (reader !== undefined && IsReadableStreamBYOBReader(reader)) {\n    const readIntoRequests = reader._readIntoRequests;\n    reader._readIntoRequests = new SimpleQueue();\n    readIntoRequests.forEach(readIntoRequest => {\n      readIntoRequest._closeSteps(undefined);\n    });\n  }\n\n  const sourceCancelPromise = stream._readableStreamController[CancelSteps](reason);\n  return transformPromiseWith(sourceCancelPromise, noop);\n}\n\nexport function ReadableStreamClose<R>(stream: ReadableStream<R>): void {\n  assert(stream._state === 'readable');\n\n  stream._state = 'closed';\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseResolve(reader);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    const readRequests = reader._readRequests;\n    reader._readRequests = new SimpleQueue();\n    readRequests.forEach(readRequest => {\n      readRequest._closeSteps();\n    });\n  }\n}\n\nexport function ReadableStreamError<R>(stream: ReadableStream<R>, e: any): void {\n  assert(IsReadableStream(stream));\n  assert(stream._state === 'readable');\n\n  stream._state = 'errored';\n  stream._storedError = e;\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseReject(reader, e);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    ReadableStreamDefaultReaderErrorReadRequests(reader, e);\n  } else {\n    assert(IsReadableStreamBYOBReader(reader));\n    ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e);\n  }\n}\n\n// Readers\n\nexport type ReadableStreamReader<R> = ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader;\n\nexport {\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader\n};\n\n// Controllers\n\nexport {\n  ReadableStreamDefaultController,\n  ReadableStreamBYOBRequest,\n  ReadableByteStreamController\n};\n\n// Helper functions for the ReadableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStream.prototype.${name} can only be used on a ReadableStream`);\n}\n", "import { assertDictionary, assertRequiredField } from './basic';\nimport { ReadableStream } from '../readable-stream';\nimport { WritableStream } from '../writable-stream';\nimport { assertReadableStream } from './readable-stream';\nimport { assertWritableStream } from './writable-stream';\n\nexport function convertReadableWritablePair<RS extends ReadableStream, WS extends WritableStream>(\n  pair: { readable: RS; writable: WS } | null | undefined,\n  context: string\n): { readable: RS; writable: WS } {\n  assertDictionary(pair, context);\n\n  const readable = pair?.readable;\n  assertRequiredField(readable, 'readable', 'ReadableWritablePair');\n  assertReadableStream(readable, `${context} has member 'readable' that`);\n\n  const writable = pair?.writable;\n  assertRequiredField(writable, 'writable', 'ReadableWritablePair');\n  assertWritableStream(writable, `${context} has member 'writable' that`);\n\n  return { readable, writable };\n}\n", "import { assertDictionary } from './basic';\nimport type {\n  ReadableStreamIteratorOptions,\n  ValidatedReadableStreamIteratorOptions\n} from '../readable-stream/iterator-options';\n\nexport function convertIteratorOptions(options: ReadableStreamIteratorOptions | null | undefined,\n                                       context: string): ValidatedReadableStreamIteratorOptions {\n  assertDictionary(options, context);\n  const preventCancel = options?.preventCancel;\n  return { preventCancel: Boolean(preventCancel) };\n}\n", "import type { QueuingStrategyInit } from '../queuing-strategy';\nimport { assertDictionary, assertRequiredField, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategyInit(init: QueuingStrategyInit | null | undefined,\n                                           context: string): QueuingStrategyInit {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  assertRequiredField(highWaterMark, 'highWaterMark', 'QueuingStrategyInit');\n  return {\n    highWaterMark: convertUnrestrictedDouble(highWaterMark)\n  };\n}\n", "import type { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst byteLengthSizeFunction = (chunk: ArrayBufferView): number => {\n  return chunk.byteLength;\n};\nsetFunctionName(byteLengthSizeFunction, 'size');\n\n/**\n * A queuing strategy that counts the number of bytes in each chunk.\n *\n * @public\n */\nexport default class ByteLengthQueuingStrategy implements QueuingStrategy<ArrayBufferView> {\n  /** @internal */\n  readonly _byteLengthQueuingStrategyHighWaterMark: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'ByteLengthQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._byteLengthQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('highWaterMark');\n    }\n    return this._byteLengthQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by returning the value of its `byteLength` property.\n   */\n  get size(): (chunk: ArrayBufferView) => number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('size');\n    }\n    return byteLengthSizeFunction;\n  }\n}\n\nObject.defineProperties(ByteLengthQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ByteLengthQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'ByteLengthQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the ByteLengthQueuingStrategy.\n\nfunction byteLengthBrandCheckException(name: string): TypeError {\n  return new TypeError(`ByteLengthQueuingStrategy.prototype.${name} can only be used on a ByteLengthQueuingStrategy`);\n}\n\nexport function IsByteLengthQueuingStrategy(x: any): x is ByteLengthQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_byteLengthQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof ByteLengthQueuingStrategy;\n}\n", "import type { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst countSizeFunction = (): 1 => {\n  return 1;\n};\nsetFunctionName(countSizeFunction, 'size');\n\n/**\n * A queuing strategy that counts the number of chunks.\n *\n * @public\n */\nexport default class CountQueuingStrategy implements QueuingStrategy<any> {\n  /** @internal */\n  readonly _countQueuingStrategyHighWaterMark!: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'CountQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._countQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('highWaterMark');\n    }\n    return this._countQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by always returning 1.\n   * This ensures that the total queue size is a count of the number of chunks in the queue.\n   */\n  get size(): (chunk: any) => 1 {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('size');\n    }\n    return countSizeFunction;\n  }\n}\n\nObject.defineProperties(CountQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(CountQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'CountQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the CountQueuingStrategy.\n\nfunction countBrandCheckException(name: string): TypeError {\n  return new TypeError(`CountQueuingStrategy.prototype.${name} can only be used on a CountQueuingStrategy`);\n}\n\nexport function IsCountQueuingStrategy(x: any): x is CountQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_countQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof CountQueuingStrategy;\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from '../transform-stream/transformer';\nimport { TransformStreamDefaultController } from '../transform-stream';\n\nexport function convertTransformer<I, O>(original: Transformer<I, O> | null,\n                                         context: string): ValidatedTransformer<I, O> {\n  assertDictionary(original, context);\n  const cancel = original?.cancel;\n  const flush = original?.flush;\n  const readableType = original?.readableType;\n  const start = original?.start;\n  const transform = original?.transform;\n  const writableType = original?.writableType;\n  return {\n    cancel: cancel === undefined ?\n      undefined :\n      convertTransformerCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    flush: flush === undefined ?\n      undefined :\n      convertTransformerFlushCallback(flush, original!, `${context} has member 'flush' that`),\n    readableType,\n    start: start === undefined ?\n      undefined :\n      convertTransformerStartCallback(start, original!, `${context} has member 'start' that`),\n    transform: transform === undefined ?\n      undefined :\n      convertTransformerTransformCallback(transform, original!, `${context} has member 'transform' that`),\n    writableType\n  };\n}\n\nfunction convertTransformerFlushCallback<I, O>(\n  fn: TransformerFlushCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): (controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertTransformerStartCallback<I, O>(\n  fn: TransformerStartCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): TransformerStartCallback<O> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertTransformerTransformCallback<I, O>(\n  fn: TransformerTransformCallback<I, O>,\n  original: Transformer<I, O>,\n  context: string\n): (chunk: I, controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: I, controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [chunk, controller]);\n}\n\nfunction convertTransformerCancelCallback<I, O>(\n  fn: TransformerCancelCallback,\n  original: Transformer<I, O>,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith,\n  uponPromise\n} from './helpers/webidl';\nimport { CreateReadableStream, type DefaultReadableStream, ReadableStream } from './readable-stream';\nimport {\n  ReadableStreamDefaultControllerCanCloseOrEnqueue,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError,\n  ReadableStreamDefaultControllerGetDesiredSize,\n  ReadableStreamDefaultControllerHasBackpressure\n} from './readable-stream/default-controller';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { CreateWritableStream, WritableStream, WritableStreamDefaultControllerErrorIfNeeded } from './writable-stream';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from './transform-stream/transformer';\nimport { convertTransformer } from './validators/transformer';\n\n// Class TransformStream\n\n/**\n * A transform stream consists of a pair of streams: a {@link WritableStream | writable stream},\n * known as its writable side, and a {@link ReadableStream | readable stream}, known as its readable side.\n * In a manner specific to the transform stream in question, writes to the writable side result in new data being\n * made available for reading from the readable side.\n *\n * @public\n */\nexport class TransformStream<I = any, O = any> {\n  /** @internal */\n  _writable!: WritableStream<I>;\n  /** @internal */\n  _readable!: DefaultReadableStream<O>;\n  /** @internal */\n  _backpressure!: boolean;\n  /** @internal */\n  _backpressureChangePromise!: Promise<void>;\n  /** @internal */\n  _backpressureChangePromise_resolve!: () => void;\n  /** @internal */\n  _transformStreamController!: TransformStreamDefaultController<O>;\n\n  constructor(\n    transformer?: Transformer<I, O>,\n    writableStrategy?: QueuingStrategy<I>,\n    readableStrategy?: QueuingStrategy<O>\n  );\n  constructor(rawTransformer: Transformer<I, O> | null | undefined = {},\n              rawWritableStrategy: QueuingStrategy<I> | null | undefined = {},\n              rawReadableStrategy: QueuingStrategy<O> | null | undefined = {}) {\n    if (rawTransformer === undefined) {\n      rawTransformer = null;\n    }\n\n    const writableStrategy = convertQueuingStrategy(rawWritableStrategy, 'Second parameter');\n    const readableStrategy = convertQueuingStrategy(rawReadableStrategy, 'Third parameter');\n\n    const transformer = convertTransformer(rawTransformer, 'First parameter');\n    if (transformer.readableType !== undefined) {\n      throw new RangeError('Invalid readableType specified');\n    }\n    if (transformer.writableType !== undefined) {\n      throw new RangeError('Invalid writableType specified');\n    }\n\n    const readableHighWaterMark = ExtractHighWaterMark(readableStrategy, 0);\n    const readableSizeAlgorithm = ExtractSizeAlgorithm(readableStrategy);\n    const writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1);\n    const writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy);\n\n    let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n    const startPromise = newPromise<void>(resolve => {\n      startPromise_resolve = resolve;\n    });\n\n    InitializeTransformStream(\n      this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm\n    );\n    SetUpTransformStreamDefaultControllerFromTransformer(this, transformer);\n\n    if (transformer.start !== undefined) {\n      startPromise_resolve(transformer.start(this._transformStreamController));\n    } else {\n      startPromise_resolve(undefined);\n    }\n  }\n\n  /**\n   * The readable side of the transform stream.\n   */\n  get readable(): ReadableStream<O> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('readable');\n    }\n\n    return this._readable;\n  }\n\n  /**\n   * The writable side of the transform stream.\n   */\n  get writable(): WritableStream<I> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('writable');\n    }\n\n    return this._writable;\n  }\n}\n\nObject.defineProperties(TransformStream.prototype, {\n  readable: { enumerable: true },\n  writable: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStream.prototype, Symbol.toStringTag, {\n    value: 'TransformStream',\n    configurable: true\n  });\n}\n\nexport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerStartCallback,\n  TransformerFlushCallback,\n  TransformerTransformCallback\n};\n\n// Transform Stream Abstract Operations\n\nexport function CreateTransformStream<I, O>(startAlgorithm: () => void | PromiseLike<void>,\n                                            transformAlgorithm: (chunk: I) => Promise<void>,\n                                            flushAlgorithm: () => Promise<void>,\n                                            cancelAlgorithm: (reason: any) => Promise<void>,\n                                            writableHighWaterMark = 1,\n                                            writableSizeAlgorithm: QueuingStrategySizeCallback<I> = () => 1,\n                                            readableHighWaterMark = 0,\n                                            readableSizeAlgorithm: QueuingStrategySizeCallback<O> = () => 1) {\n  assert(IsNonNegativeNumber(writableHighWaterMark));\n  assert(IsNonNegativeNumber(readableHighWaterMark));\n\n  const stream: TransformStream<I, O> = Object.create(TransformStream.prototype);\n\n  let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n  const startPromise = newPromise<void>(resolve => {\n    startPromise_resolve = resolve;\n  });\n\n  InitializeTransformStream(stream, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark,\n                            readableSizeAlgorithm);\n\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm);\n\n  const startResult = startAlgorithm();\n  startPromise_resolve(startResult);\n  return stream;\n}\n\nfunction InitializeTransformStream<I, O>(stream: TransformStream<I, O>,\n                                         startPromise: Promise<void>,\n                                         writableHighWaterMark: number,\n                                         writableSizeAlgorithm: QueuingStrategySizeCallback<I>,\n                                         readableHighWaterMark: number,\n                                         readableSizeAlgorithm: QueuingStrategySizeCallback<O>) {\n  function startAlgorithm(): Promise<void> {\n    return startPromise;\n  }\n\n  function writeAlgorithm(chunk: I): Promise<void> {\n    return TransformStreamDefaultSinkWriteAlgorithm(stream, chunk);\n  }\n\n  function abortAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSinkAbortAlgorithm(stream, reason);\n  }\n\n  function closeAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSinkCloseAlgorithm(stream);\n  }\n\n  stream._writable = CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm,\n                                          writableHighWaterMark, writableSizeAlgorithm);\n\n  function pullAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSourcePullAlgorithm(stream);\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSourceCancelAlgorithm(stream, reason);\n  }\n\n  stream._readable = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, readableHighWaterMark,\n                                          readableSizeAlgorithm);\n\n  // The [[backpressure]] slot is set to undefined so that it can be initialised by TransformStreamSetBackpressure.\n  stream._backpressure = undefined!;\n  stream._backpressureChangePromise = undefined!;\n  stream._backpressureChangePromise_resolve = undefined!;\n  TransformStreamSetBackpressure(stream, true);\n\n  stream._transformStreamController = undefined!;\n}\n\nfunction IsTransformStream(x: unknown): x is TransformStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_transformStreamController')) {\n    return false;\n  }\n\n  return x instanceof TransformStream;\n}\n\n// This is a no-op if both sides are already errored.\nfunction TransformStreamError(stream: TransformStream, e: any) {\n  ReadableStreamDefaultControllerError(stream._readable._readableStreamController, e);\n  TransformStreamErrorWritableAndUnblockWrite(stream, e);\n}\n\nfunction TransformStreamErrorWritableAndUnblockWrite(stream: TransformStream, e: any) {\n  TransformStreamDefaultControllerClearAlgorithms(stream._transformStreamController);\n  WritableStreamDefaultControllerErrorIfNeeded(stream._writable._writableStreamController, e);\n  TransformStreamUnblockWrite(stream);\n}\n\nfunction TransformStreamUnblockWrite(stream: TransformStream) {\n  if (stream._backpressure) {\n    // Pretend that pull() was called to permit any pending write() calls to complete. TransformStreamSetBackpressure()\n    // cannot be called from enqueue() or pull() once the ReadableStream is errored, so this will will be the final time\n    // _backpressure is set.\n    TransformStreamSetBackpressure(stream, false);\n  }\n}\n\nfunction TransformStreamSetBackpressure(stream: TransformStream, backpressure: boolean) {\n  // Passes also when called during construction.\n  assert(stream._backpressure !== backpressure);\n\n  if (stream._backpressureChangePromise !== undefined) {\n    stream._backpressureChangePromise_resolve();\n  }\n\n  stream._backpressureChangePromise = newPromise(resolve => {\n    stream._backpressureChangePromise_resolve = resolve;\n  });\n\n  stream._backpressure = backpressure;\n}\n\n// Class TransformStreamDefaultController\n\n/**\n * Allows control of the {@link ReadableStream} and {@link WritableStream} of the associated {@link TransformStream}.\n *\n * @public\n */\nexport class TransformStreamDefaultController<O> {\n  /** @internal */\n  _controlledTransformStream: TransformStream<any, O>;\n  /** @internal */\n  _finishPromise: Promise<undefined> | undefined;\n  /** @internal */\n  _finishPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _finishPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _transformAlgorithm: (chunk: any) => Promise<void>;\n  /** @internal */\n  _flushAlgorithm: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the readable side’s internal queue. It can be negative, if the queue is over-full.\n   */\n  get desiredSize(): number | null {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    const readableController = this._controlledTransformStream._readable._readableStreamController;\n    return ReadableStreamDefaultControllerGetDesiredSize(readableController);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the readable side of the controlled transform stream.\n   */\n  enqueue(chunk: O): void;\n  enqueue(chunk: O = undefined!): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    TransformStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors both the readable side and the writable side of the controlled transform stream, making all future\n   * interactions with it fail with the given error `e`. Any chunks queued for transformation will be discarded.\n   */\n  error(reason: any = undefined): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    TransformStreamDefaultControllerError(this, reason);\n  }\n\n  /**\n   * Closes the readable side and errors the writable side of the controlled transform stream. This is useful when the\n   * transformer only needs to consume a portion of the chunks written to the writable side.\n   */\n  terminate(): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('terminate');\n    }\n\n    TransformStreamDefaultControllerTerminate(this);\n  }\n}\n\nObject.defineProperties(TransformStreamDefaultController.prototype, {\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  terminate: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(TransformStreamDefaultController.prototype.enqueue, 'enqueue');\nsetFunctionName(TransformStreamDefaultController.prototype.error, 'error');\nsetFunctionName(TransformStreamDefaultController.prototype.terminate, 'terminate');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'TransformStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Transform Stream Default Controller Abstract Operations\n\nfunction IsTransformStreamDefaultController<O = any>(x: any): x is TransformStreamDefaultController<O> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledTransformStream')) {\n    return false;\n  }\n\n  return x instanceof TransformStreamDefaultController;\n}\n\nfunction SetUpTransformStreamDefaultController<I, O>(stream: TransformStream<I, O>,\n                                                     controller: TransformStreamDefaultController<O>,\n                                                     transformAlgorithm: (chunk: I) => Promise<void>,\n                                                     flushAlgorithm: () => Promise<void>,\n                                                     cancelAlgorithm: (reason: any) => Promise<void>) {\n  assert(IsTransformStream(stream));\n  assert(stream._transformStreamController === undefined);\n\n  controller._controlledTransformStream = stream;\n  stream._transformStreamController = controller;\n\n  controller._transformAlgorithm = transformAlgorithm;\n  controller._flushAlgorithm = flushAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._finishPromise = undefined;\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\nfunction SetUpTransformStreamDefaultControllerFromTransformer<I, O>(stream: TransformStream<I, O>,\n                                                                    transformer: ValidatedTransformer<I, O>) {\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  let transformAlgorithm: (chunk: I) => Promise<void>;\n  let flushAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (transformer.transform !== undefined) {\n    transformAlgorithm = chunk => transformer.transform!(chunk, controller);\n  } else {\n    transformAlgorithm = chunk => {\n      try {\n        TransformStreamDefaultControllerEnqueue(controller, chunk as unknown as O);\n        return promiseResolvedWith(undefined);\n      } catch (transformResultE) {\n        return promiseRejectedWith(transformResultE);\n      }\n    };\n  }\n\n  if (transformer.flush !== undefined) {\n    flushAlgorithm = () => transformer.flush!(controller);\n  } else {\n    flushAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  if (transformer.cancel !== undefined) {\n    cancelAlgorithm = reason => transformer.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm);\n}\n\nfunction TransformStreamDefaultControllerClearAlgorithms(controller: TransformStreamDefaultController<any>) {\n  controller._transformAlgorithm = undefined!;\n  controller._flushAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\nfunction TransformStreamDefaultControllerEnqueue<O>(controller: TransformStreamDefaultController<O>, chunk: O) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController;\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(readableController)) {\n    throw new TypeError('Readable side is not in a state that permits enqueue');\n  }\n\n  // We throttle transform invocations based on the backpressure of the ReadableStream, but we still\n  // accept TransformStreamDefaultControllerEnqueue() calls.\n\n  try {\n    ReadableStreamDefaultControllerEnqueue(readableController, chunk);\n  } catch (e) {\n    // This happens when readableStrategy.size() throws.\n    TransformStreamErrorWritableAndUnblockWrite(stream, e);\n\n    throw stream._readable._storedError;\n  }\n\n  const backpressure = ReadableStreamDefaultControllerHasBackpressure(readableController);\n  if (backpressure !== stream._backpressure) {\n    assert(backpressure);\n    TransformStreamSetBackpressure(stream, true);\n  }\n}\n\nfunction TransformStreamDefaultControllerError(controller: TransformStreamDefaultController<any>, e: any) {\n  TransformStreamError(controller._controlledTransformStream, e);\n}\n\nfunction TransformStreamDefaultControllerPerformTransform<I, O>(controller: TransformStreamDefaultController<O>,\n                                                                chunk: I) {\n  const transformPromise = controller._transformAlgorithm(chunk);\n  return transformPromiseWith(transformPromise, undefined, r => {\n    TransformStreamError(controller._controlledTransformStream, r);\n    throw r;\n  });\n}\n\nfunction TransformStreamDefaultControllerTerminate<O>(controller: TransformStreamDefaultController<O>) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController;\n\n  ReadableStreamDefaultControllerClose(readableController);\n\n  const error = new TypeError('TransformStream terminated');\n  TransformStreamErrorWritableAndUnblockWrite(stream, error);\n}\n\n// TransformStreamDefaultSink Algorithms\n\nfunction TransformStreamDefaultSinkWriteAlgorithm<I, O>(stream: TransformStream<I, O>, chunk: I): Promise<void> {\n  assert(stream._writable._state === 'writable');\n\n  const controller = stream._transformStreamController;\n\n  if (stream._backpressure) {\n    const backpressureChangePromise = stream._backpressureChangePromise;\n    assert(backpressureChangePromise !== undefined);\n    return transformPromiseWith(backpressureChangePromise, () => {\n      const writable = stream._writable;\n      const state = writable._state;\n      if (state === 'erroring') {\n        throw writable._storedError;\n      }\n      assert(state === 'writable');\n      return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n    });\n  }\n\n  return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n}\n\nfunction TransformStreamDefaultSinkAbortAlgorithm<I, O>(stream: TransformStream<I, O>, reason: any): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  // Assign the _finishPromise now so that if _cancelAlgorithm calls readable.cancel() internally,\n  // we don't run the _cancelAlgorithm again.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const cancelPromise = controller._cancelAlgorithm(reason);\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(cancelPromise, () => {\n    if (readable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, readable._storedError);\n    } else {\n      ReadableStreamDefaultControllerError(readable._readableStreamController, reason);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    ReadableStreamDefaultControllerError(readable._readableStreamController, r);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\nfunction TransformStreamDefaultSinkCloseAlgorithm<I, O>(stream: TransformStream<I, O>): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  // Assign the _finishPromise now so that if _flushAlgorithm calls readable.cancel() internally,\n  // we don't also run the _cancelAlgorithm.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const flushPromise = controller._flushAlgorithm();\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(flushPromise, () => {\n    if (readable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, readable._storedError);\n    } else {\n      ReadableStreamDefaultControllerClose(readable._readableStreamController);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    ReadableStreamDefaultControllerError(readable._readableStreamController, r);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\n// TransformStreamDefaultSource Algorithms\n\nfunction TransformStreamDefaultSourcePullAlgorithm(stream: TransformStream): Promise<void> {\n  // Invariant. Enforced by the promises returned by start() and pull().\n  assert(stream._backpressure);\n\n  assert(stream._backpressureChangePromise !== undefined);\n\n  TransformStreamSetBackpressure(stream, false);\n\n  // Prevent the next pull() call until there is backpressure.\n  return stream._backpressureChangePromise;\n}\n\nfunction TransformStreamDefaultSourceCancelAlgorithm<I, O>(stream: TransformStream<I, O>, reason: any): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._writable cannot change after construction, so caching it across a call to user code is safe.\n  const writable = stream._writable;\n\n  // Assign the _finishPromise now so that if _flushAlgorithm calls writable.abort() or\n  // writable.cancel() internally, we don't run the _cancelAlgorithm again, or also run the\n  // _flushAlgorithm.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const cancelPromise = controller._cancelAlgorithm(reason);\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(cancelPromise, () => {\n    if (writable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, writable._storedError);\n    } else {\n      WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, reason);\n      TransformStreamUnblockWrite(stream);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, r);\n    TransformStreamUnblockWrite(stream);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\n// Helper functions for the TransformStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStreamDefaultController.prototype.${name} can only be used on a TransformStreamDefaultController`);\n}\n\nexport function defaultControllerFinishPromiseResolve(controller: TransformStreamDefaultController<any>) {\n  if (controller._finishPromise_resolve === undefined) {\n    return;\n  }\n\n  controller._finishPromise_resolve();\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\nexport function defaultControllerFinishPromiseReject(controller: TransformStreamDefaultController<any>, reason: any) {\n  if (controller._finishPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(controller._finishPromise!);\n  controller._finishPromise_reject(reason);\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\n// Helper functions for the TransformStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStream.prototype.${name} can only be used on a TransformStream`);\n}\n", "import {\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n  ReadableByteStreamController,\n  ReadableStream,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultController,\n  ReadableStreamDefaultReader,\n  TransformStream,\n  TransformStreamDefaultController,\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter\n} from './ponyfill';\nimport { globals } from './globals';\n\n// Export\nexport * from './ponyfill';\n\nconst exports = {\n  ReadableStream,\n  ReadableStreamDefaultController,\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader,\n\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter,\n\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n\n  TransformStream,\n  TransformStreamDefaultController\n};\n\n// Add classes to global scope\nif (typeof globals !== 'undefined') {\n  for (const prop in exports) {\n    if (Object.prototype.hasOwnProperty.call(exports, prop)) {\n      Object.defineProperty(globals, prop, {\n        value: exports[prop as (keyof typeof exports)],\n        writable: true,\n        configurable: true\n      });\n    }\n  }\n}\n"], "names": ["SymbolPolyfill", "Symbol", "iterator", "description", "concat", "__generator", "thisArg", "body", "f", "y", "t", "g", "_", "label", "sent", "trys", "ops", "next", "verb", "throw", "return", "this", "n", "v", "op", "TypeError", "call", "done", "value", "pop", "length", "push", "e", "step", "__values", "o", "s", "m", "i", "__await", "__asyncGenerator", "_arguments", "generator", "asyncIterator", "apply", "q", "Promise", "a", "b", "resume", "r", "resolve", "then", "fulfill", "reject", "settle", "shift", "__asyncDelegator", "p", "__asyncValues", "d", "noop", "typeIsObject", "x", "SuppressedError", "rethrowAssertionErrorRejection", "setFunctionName", "fn", "name", "Object", "defineProperty", "configurable", "_a", "originalPromise", "originalPromiseThen", "prototype", "originalPromiseReject", "bind", "newPromise", "executor", "promiseResolvedWith", "promiseRejectedWith", "reason", "PerformPromiseThen", "promise", "onFulfilled", "onRejected", "uponPromise", "undefined", "uponFulfillment", "uponRejection", "transformPromiseWith", "fulfillmentH<PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>", "setPromiseIsHandledToTrue", "_queueMicrotask", "callback", "queueMicrotask", "resolvedPromise_1", "cb", "reflectCall", "F", "V", "args", "Function", "promiseCall", "SimpleQueue", "_cursor", "_size", "_front", "_elements", "_next", "_back", "get", "element", "oldBack", "newBack", "QUEUE_MAX_ARRAY_SIZE", "oldFront", "newFront", "old<PERSON>ursor", "newCursor", "elements", "for<PERSON>ach", "node", "peek", "front", "cursor", "AbortSteps", "ErrorSteps", "CancelSteps", "PullSteps", "ReleaseSteps", "ReadableStreamReaderGenericInitialize", "reader", "stream", "_ownerReadableStream", "_reader", "_state", "defaultReaderClosedPromiseInitialize", "defaultReaderClosedPromiseResolve", "defaultReaderClosedPromiseInitializeAsResolved", "defaultReaderClosedPromiseInitializeAsRejected", "_storedError", "ReadableStreamReaderGenericCancel", "ReadableStreamCancel", "ReadableStreamReaderGenericRelease", "defaultReaderClosedPromiseReject", "defaultReaderClosedPromiseResetToRejected", "_readableStreamController", "readerLockException", "_closedPromise", "_closedPromise_resolve", "_closedPromise_reject", "NumberIsFinite", "Number", "isFinite", "MathTrunc", "Math", "trunc", "ceil", "floor", "assertDictionary", "obj", "context", "assertFunction", "assertObject", "isObject", "assertRequiredArgument", "position", "assertRequiredField", "field", "convertUnrestrictedDouble", "censorNegativeZero", "convertUnsignedLongLongWithEnforceRange", "upperBound", "MAX_SAFE_INTEGER", "integerPart", "assertReadableStream", "IsReadableStream", "AcquireReadableStreamDefaultReader", "ReadableStreamDefaultReader", "ReadableStreamAddReadRequest", "readRequest", "_readRequests", "ReadableStreamFulfillReadRequest", "chunk", "_closeSteps", "_chunkSteps", "ReadableStreamGetNumReadRequests", "ReadableStreamHasDefaultReader", "IsReadableStreamDefaultReader", "IsReadableStreamLocked", "defaultReaderBrandCheckException", "cancel", "read", "resolvePromise", "rejectPromise", "ReadableStreamDefaultReaderRead", "_errorSteps", "releaseLock", "ReadableStreamDefaultReaderErrorReadRequests", "ReadableStreamDefaultReaderRelease", "hasOwnProperty", "_disturbed", "readRequests", "CreateArrayFromList", "slice", "CopyDataBlockBytes", "dest", "destOffset", "src", "srcOffset", "Uint8Array", "set", "defineProperties", "enumerable", "closed", "toStringTag", "TransferArrayBuffer", "O", "transfer", "buffer", "structuredClone", "IsDetachedBuffer", "detached", "byteLength", "ArrayBufferSlice", "begin", "end", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GetMethod", "receiver", "prop", "func", "String", "SymbolAsyncIterator", "_c", "_b", "for", "GetIterator", "hint", "method", "syncIteratorRecord", "syncIterable", "next<PERSON><PERSON><PERSON>", "CreateAsyncFromSyncIterator", "AsyncIteratorPrototype", "ReadableStreamAsyncIteratorImpl", "preventCancel", "_ongoingPromise", "_isFinished", "_preventCancel", "_this", "nextSteps", "_nextSteps", "returnSteps", "_returnSteps", "result", "ReadableStreamAsyncIteratorPrototype", "IsReadableStreamAsyncIterator", "_asyncIteratorImpl", "streamAsyncIteratorBrandCheckException", "setPrototypeOf", "NumberIsNaN", "isNaN", "CloneAsUint8Array", "byteOffset", "DequeueValue", "container", "pair", "_queue", "_queueTotalSize", "size", "EnqueueValueWithSize", "Infinity", "RangeError", "ResetQueue", "isDataViewConstructor", "ctor", "DataView", "ReadableStreamBYOBRequest", "IsReadableStreamBYOBRequest", "byobRequestBrandCheckException", "_view", "respond", "bytes<PERSON>ritten", "_associatedReadableByteStreamController", "ReadableByteStreamControllerRespond", "respondWithNewView", "view", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerRespondWithNewView", "ReadableByteStreamController", "IsReadableByteStreamController", "byteStreamControllerBrandCheckException", "ReadableByteStreamControllerGetBYOBRequest", "ReadableByteStreamControllerGetDesiredSize", "close", "_closeRequested", "state", "_controlledReadableByteStream", "ReadableByteStreamControllerClose", "enqueue", "ReadableByteStreamControllerEnqueue", "error", "ReadableByteStreamControllerError", "ReadableByteStreamControllerClearPendingPullIntos", "_cancelAlgorithm", "ReadableByteStreamControllerClearAlgorithms", "ReadableByteStreamControllerFillReadRequestFromQueue", "autoAllocateChunkSize", "_autoAllocateChunkSize", "bufferE", "pullIntoDescriptor", "bufferByteLength", "bytesFilled", "minimumFill", "elementSize", "viewConstructor", "readerType", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerCallPullIfNeeded", "firstPullInto", "controller", "shouldPull", "_started", "ReadableStreamHasBYOBReader", "ReadableStreamGetNumReadIntoRequests", "desiredSize", "ReadableByteStreamControllerShouldCallPull", "_pulling", "_pullAgain", "_pullAlgorithm", "ReadableByteStreamControllerInvalidateBYOBRequest", "ReadableByteStreamControllerCommitPullIntoDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerConvertPullIntoDescriptor", "readIntoRequest", "_readIntoRequests", "ReadableStreamFulfillReadIntoRequest", "ReadableByteStreamControllerEnqueueChunkToQueue", "ReadableByteStreamControllerEnqueueClonedChunkToQueue", "clonedChunk", "cloneE", "ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue", "firstDescriptor", "ReadableByteStreamControllerShiftPendingPullInto", "ReadableByteStreamControllerFillPullIntoDescriptorFromQueue", "maxBytesToCopy", "min", "maxBytesFilled", "totalBytesToCopyRemaining", "ready", "maxAlignedBytes", "queue", "headOfQueue", "bytesToCopy", "destStart", "ReadableByteStreamControllerFillHeadPullIntoDescriptor", "ReadableByteStreamControllerHandleQueueDrain", "ReadableStreamClose", "_byobRequest", "ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue", "ReadableByteStreamControllerPullInto", "constructor", "BYTES_PER_ELEMENT", "arrayBufferViewElementSize", "ReadableStreamAddReadIntoRequest", "emptyView", "ReadableByteStreamControllerRespondInternal", "ReadableByteStreamControllerRespondInClosedState", "remainderSize", "ReadableByteStreamControllerRespondInReadableState", "firstPendingPullInto", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerProcessReadRequestsUsingQueue", "ReadableStreamError", "entry", "byobRequest", "create", "request", "SetUpReadableStreamBYOBRequest", "_strategyHWM", "viewByteLength", "SetUpReadableByteStreamController", "startAlgorithm", "pullAlgorithm", "cancelAlgorithm", "highWaterMark", "convertReadableStreamReaderMode", "mode", "AcquireReadableStreamBYOBReader", "ReadableStreamBYOBReader", "IsReadableStreamBYOBReader", "byobReaderBrandCheckException", "rawOptions", "options", "convertByobReadOptions", "isDataView", "ReadableStreamBYOBReaderRead", "ReadableStreamBYOBReaderErrorReadIntoRequests", "ReadableStreamBYOBReaderRelease", "readIntoRequests", "ExtractHighWaterMark", "strategy", "defaultHWM", "ExtractSizeAlgorithm", "convertQueuingStrategy", "init", "convertQueuingStrategySize", "convertUnderlyingSinkAbortCallback", "original", "convertUnderlyingSinkCloseCallback", "convertUnderlyingSinkStartCallback", "convertUnderlyingSinkWriteCallback", "assertWritableStream", "IsWritableStream", "supportsAbortController", "AbortController", "WritableStream", "rawUnderlyingSink", "rawStrategy", "underlyingSink", "abort", "start", "type", "write", "convertUnderlyingSink", "InitializeWritableStream", "sizeAlgorithm", "writeAlgorithm", "closeAlgorithm", "abortAlgorithm", "WritableStreamDefaultController", "SetUpWritableStreamDefaultController", "SetUpWritableStreamDefaultControllerFromUnderlyingSink", "streamBrandCheckException", "IsWritableStreamLocked", "WritableStreamAbort", "WritableStreamCloseQueuedOrInFlight", "WritableStreamClose", "getWriter", "AcquireWritableStreamDefaultWriter", "WritableStreamDefaultWriter", "_writer", "_writableStreamController", "_writeRequests", "_inFlightWriteRequest", "_closeRequest", "_inFlightCloseRequest", "_pendingAbortRequest", "_backpressure", "_abortReason", "_abortController", "_promise", "wasAlreadyErroring", "_resolve", "_reject", "_reason", "_wasAlreadyErroring", "WritableStreamStartErroring", "closeRequest", "writer", "defaultWriterReadyPromiseResolve", "closeSentinel", "WritableStreamDefaultControllerAdvanceQueueIfNeeded", "WritableStreamDealWithRejection", "WritableStreamFinishErroring", "WritableStreamDefaultWriterEnsureReadyPromiseRejected", "WritableStreamHasOperationMarkedInFlight", "storedError", "writeRequest", "abortRequest", "WritableStreamRejectCloseAndClosedPromiseIfNeeded", "defaultWriterClosedPromiseReject", "WritableStreamUpdateBackpressure", "backpressure", "defaultWriterReadyPromiseInitialize", "defaultWriterReadyPromiseReset", "locked", "_ownerWritableStream", "defaultWriterReadyPromiseInitializeAsResolved", "defaultWriterClosedPromiseInitialize", "defaultWriterReadyPromiseInitializeAsRejected", "defaultWriterClosedPromiseResolve", "defaultWriterClosedPromiseInitializeAsRejected", "IsWritableStreamDefaultWriter", "defaultWriterBrandCheckException", "defaultWriterLockException", "WritableStreamDefaultControllerGetDesiredSize", "WritableStreamDefaultWriterGetDesiredSize", "_readyPromise", "WritableStreamDefaultWriterAbort", "WritableStreamDefaultWriterClose", "WritableStreamDefaultWriterRelease", "WritableStreamDefaultWriterWrite", "WritableStreamDefaultWriterEnsureClosedPromiseRejected", "_closedPromiseState", "defaultWriterClosedPromiseResetToRejected", "_readyPromiseState", "defaultWriterReadyPromiseReject", "defaultWriterReadyPromiseResetToRejected", "releasedError", "chunkSize", "_strategySizeAlgorithm", "chunkSizeE", "WritableStreamDefaultControllerErrorIfNeeded", "WritableStreamDefaultControllerGetChunkSize", "WritableStreamAddWriteRequest", "enqueueE", "_controlledWritableStream", "WritableStreamDefaultControllerGetBackpressure", "WritableStreamDefaultControllerWrite", "IsWritableStreamDefaultController", "defaultControllerBrandCheckException", "signal", "WritableStreamDefaultControllerError", "_abortAlgorithm", "WritableStreamDefaultControllerClearAlgorithms", "createAbortController", "_writeAlgorithm", "_closeAlgorithm", "WritableStreamMarkCloseRequestInFlight", "sinkClosePromise", "WritableStreamFinishInFlightClose", "WritableStreamFinishInFlightCloseWithError", "WritableStreamDefaultControllerProcessClose", "WritableStreamMarkFirstWriteRequestInFlight", "sinkWritePromise", "WritableStreamFinishInFlightWrite", "WritableStreamFinishInFlightWriteWithError", "WritableStreamDefaultControllerProcessWrite", "_readyPromise_resolve", "_readyPromise_reject", "abortReason", "globals", "globalThis", "self", "global", "DOMException", "isDOMExceptionConstructor", "message", "Error", "captureStackTrace", "writable", "createPolyfill", "ReadableStreamPipeTo", "source", "preventClose", "preventAbort", "shuttingDown", "currentWrite", "action", "actions", "shutdownWithAction", "all", "map", "aborted", "addEventListener", "isOrBecomesErrored", "shutdown", "WritableStreamDefaultWriterCloseWithErrorPropagation", "destClosed_1", "waitForWritesToFinish", "oldCurrentWrite", "originalIsError", "originalError", "doTheRest", "finalize", "newError", "isError", "removeEventListener", "resolveLoop", "rejectLoop", "resolveRead", "rejectRead", "ReadableStreamDefaultController", "IsReadableStreamDefaultController", "ReadableStreamDefaultControllerGetDesiredSize", "ReadableStreamDefaultControllerCanCloseOrEnqueue", "ReadableStreamDefaultControllerClose", "ReadableStreamDefaultControllerEnqueue", "ReadableStreamDefaultControllerError", "ReadableStreamDefaultControllerClearAlgorithms", "_controlledReadableStream", "ReadableStreamDefaultControllerCallPullIfNeeded", "ReadableStreamDefaultControllerShouldCallPull", "SetUpReadableStreamDefaultController", "ReadableStreamTee", "cloneForBranch2", "reason1", "reason2", "branch1", "branch2", "resolveCancelPromise", "reading", "readAgainForBranch1", "readAgainForBranch2", "canceled1", "canceled2", "cancelPromise", "forwardReaderError", "thisReader", "pullWithDefaultReader", "chunk1", "chunk2", "pull1Algorithm", "pull2Algorithm", "pullWithBYOBReader", "forBranch2", "byobBranch", "otherBranch", "byobCanceled", "otherCanceled", "cancel1Algorithm", "compositeReason", "cancelResult", "cancel2Algorithm", "CreateReadableByteStream", "ReadableByteStreamTee", "readAgain", "CreateReadableStream", "ReadableStreamDefaultTee", "ReadableStreamFrom", "<PERSON><PERSON><PERSON><PERSON>", "readPromise", "readResult", "ReadableStreamFromDefaultReader", "asyncIterable", "iteratorRecord", "nextResult", "IteratorNext", "iterResult", "Boolean", "IteratorComplete", "IteratorValue", "return<PERSON><PERSON><PERSON>", "returnResult", "ReadableStreamFromIterable", "convertUnderlyingSourceCancelCallback", "convertUnderlyingSourcePullCallback", "convertUnderlyingSourceStartCallback", "convertReadableStreamType", "convertPipeOptions", "isAbortSignal", "assertAbortSignal", "ReadableStream", "rawUnderlyingSource", "underlyingSource", "pull", "convertUnderlyingDefaultOrByteSource", "InitializeReadableStream", "underlyingByteSource", "SetUpReadableByteStreamControllerFromUnderlyingSource", "SetUpReadableStreamDefaultControllerFromUnderlyingSource", "convertReaderOptions", "pipeThrough", "rawTransform", "transform", "readable", "convertReadableWritablePair", "pipeTo", "destination", "tee", "values", "impl", "convertIteratorOptions", "from", "convertQueuingStrategyInit", "byteLengthSizeFunction", "ByteLengthQueuingStrategy", "_byteLengthQueuingStrategyHighWaterMark", "IsByteLengthQueuingStrategy", "byteLengthBrandCheckException", "countSizeFunction", "CountQueuingStrategy", "_countQueuingStrategyHighWaterMark", "IsCountQueuingStrategy", "countBrandCheckException", "convertTransformerFlushCallback", "convertTransformerStartCallback", "convertTransformerTransformCallback", "convertTransformerCancelCallback", "TransformStream", "rawTransformer", "rawWritableStrategy", "rawReadableStrategy", "writableStrategy", "readableStrategy", "transformer", "flush", "readableType", "writableType", "convertTransformer", "startPromise_resolve", "readableHighWaterMark", "readableSizeAlgorithm", "writableHighWaterMark", "writableSizeAlgorithm", "startPromise", "_transformStreamController", "_backpressureChangePromise", "_writable", "TransformStreamDefaultControllerPerformTransform", "TransformStreamDefaultSinkWriteAlgorithm", "_finishPromise", "_readable", "_finishPromise_resolve", "_finishPromise_reject", "TransformStreamDefaultControllerClearAlgorithms", "defaultControllerFinishPromiseReject", "defaultControllerFinishPromiseResolve", "TransformStreamDefaultSinkAbortAlgorithm", "flushPromise", "_flushAlgorithm", "TransformStreamDefaultSinkCloseAlgorithm", "TransformStreamSetBackpressure", "TransformStreamDefaultSourcePullAlgorithm", "TransformStreamUnblockWrite", "TransformStreamDefaultSourceCancelAlgorithm", "CreateWritableStream", "_backpressureChangePromise_resolve", "InitializeTransformStream", "transformAlgorithm", "flushAlgorithm", "TransformStreamDefaultController", "TransformStreamDefaultControllerEnqueue", "transformResultE", "_controlledTransformStream", "_transformAlgorithm", "SetUpTransformStreamDefaultController", "SetUpTransformStreamDefaultControllerFromTransformer", "IsTransformStream", "TransformStreamError", "TransformStreamErrorWritableAndUnblockWrite", "IsTransformStreamDefaultController", "terminate", "TransformStreamDefaultControllerTerminate", "readableController", "ReadableStreamDefaultControllerHasBackpressure", "exports"], "mappings": ";;;;;;;0PAEA,IAAMA,EACc,mBAAXC,QAAoD,iBAApBA,OAAOC,SAC5CD,OACA,SAAAE,GAAe,MAAA,UAAAC,OAAUD,EAA+B,IAAA,ECuHrD,SAASE,EAAYC,EAASC,GACjC,IAAsGC,EAAGC,EAAGC,EAAGC,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPJ,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAK,EAAEK,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEM,KAAMC,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXjB,SAA0BU,EAAEV,OAAOC,UAAY,WAAa,OAAOmB,IAAO,GAAGV,EACvJ,SAASO,EAAKI,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIhB,EAAG,MAAM,IAAIiB,UAAU,mCAC3B,KAAOd,IAAMA,EAAI,EAAGa,EAAG,KAAOZ,EAAI,IAAKA,OACnC,GAAIJ,EAAI,EAAGC,IAAMC,EAAY,EAARc,EAAG,GAASf,EAAU,OAAIe,EAAG,GAAKf,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAEgB,KAAKjB,GAAI,GAAKA,EAAEQ,SAAWP,EAAIA,EAAEgB,KAAKjB,EAAGe,EAAG,KAAKG,KAAM,OAAOjB,EAE3J,OADID,EAAI,EAAGC,IAAGc,EAAK,CAAS,EAARA,EAAG,GAAQd,EAAEkB,QACzBJ,EAAG,IACP,KAAK,EAAG,KAAK,EAAGd,EAAIc,EAAI,MACxB,KAAK,EAAc,OAAXZ,EAAEC,QAAgB,CAAEe,MAAOJ,EAAG,GAAIG,MAAM,GAChD,KAAK,EAAGf,EAAEC,QAASJ,EAAIe,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKZ,EAAEI,IAAIa,MAAOjB,EAAEG,KAAKc,MAAO,SACxC,QACI,KAAMnB,EAAIE,EAAEG,MAAML,EAAIA,EAAEoB,OAAS,GAAKpB,EAAEA,EAAEoB,OAAS,KAAkB,IAAVN,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEZ,EAAI,EAAG,QAAW,CAC5G,GAAc,IAAVY,EAAG,MAAcd,GAAMc,EAAG,GAAKd,EAAE,IAAMc,EAAG,GAAKd,EAAE,IAAM,CAAEE,EAAEC,MAAQW,EAAG,GAAI,KAAQ,CACtF,GAAc,IAAVA,EAAG,IAAYZ,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIA,EAAIc,EAAI,KAAQ,CACrE,GAAId,GAAKE,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIE,EAAEI,IAAIe,KAAKP,GAAK,KAAQ,CAC/Dd,EAAE,IAAIE,EAAEI,IAAIa,MAChBjB,EAAEG,KAAKc,MAAO,SAEtBL,EAAKjB,EAAKmB,KAAKpB,EAASM,EAC3B,CAAC,MAAOoB,GAAKR,EAAK,CAAC,EAAGQ,GAAIvB,EAAI,CAAE,CAAW,QAAED,EAAIE,EAAI,CAAI,CAC1D,GAAY,EAARc,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAEI,MAAOJ,EAAG,GAAKA,EAAG,QAAK,EAAQG,MAAM,EAC7E,CAtB+CM,CAAK,CAACX,EAAGC,GAAM,CAAG,CAuBtE,CAkBO,SAASW,EAASC,GACrB,IAAIC,EAAsB,mBAAXnC,QAAyBA,OAAOC,SAAUmC,EAAID,GAAKD,EAAEC,GAAIE,EAAI,EAC5E,GAAID,EAAG,OAAOA,EAAEX,KAAKS,GACrB,GAAIA,GAAyB,iBAAbA,EAAEL,OAAqB,MAAO,CAC1Cb,KAAM,WAEF,OADIkB,GAAKG,GAAKH,EAAEL,SAAQK,OAAI,GACrB,CAAEP,MAAOO,GAAKA,EAAEG,KAAMX,MAAOQ,EACvC,GAEL,MAAM,IAAIV,UAAUW,EAAI,0BAA4B,kCACxD,CA6CO,SAASG,EAAQhB,GACpB,OAAOF,gBAAgBkB,GAAWlB,KAAKE,EAAIA,EAAGF,MAAQ,IAAIkB,EAAQhB,EACtE,CAEO,SAASiB,EAAiBlC,EAASmC,EAAYC,GAClD,IAAKzC,OAAO0C,cAAe,MAAM,IAAIlB,UAAU,wCAC/C,IAAoDa,EAAhD3B,EAAI+B,EAAUE,MAAMtC,EAASmC,GAAc,IAAQI,EAAI,GAC3D,OAAOP,EAAI,CAAA,EAAIpB,EAAK,QAASA,EAAK,SAAUA,EAAK,UAAWoB,EAAErC,OAAO0C,eAAiB,WAAc,OAAOtB,IAAO,EAAEiB,EACpH,SAASpB,EAAKI,GAASX,EAAEW,KAAIgB,EAAEhB,GAAK,SAAUC,GAAK,OAAO,IAAIuB,SAAQ,SAAUC,EAAGC,GAAKH,EAAEd,KAAK,CAACT,EAAGC,EAAGwB,EAAGC,IAAM,GAAKC,EAAO3B,EAAGC,EAAG,GAAM,EAAG,CAC1I,SAAS0B,EAAO3B,EAAGC,GAAK,KACV2B,EADqBvC,EAAEW,GAAGC,IACnBK,iBAAiBW,EAAUO,QAAQK,QAAQD,EAAEtB,MAAML,GAAG6B,KAAKC,EAASC,GAAUC,EAAOV,EAAE,GAAG,GAAIK,EADvE,CAAG,MAAOlB,GAAKuB,EAAOV,EAAE,GAAG,GAAIb,GAC3E,IAAckB,CADoE,CAElF,SAASG,EAAQzB,GAASqB,EAAO,OAAQrB,EAAS,CAClD,SAAS0B,EAAO1B,GAASqB,EAAO,QAASrB,EAAS,CAClD,SAAS2B,EAAO/C,EAAGe,GAASf,EAAEe,GAAIsB,EAAEW,QAASX,EAAEf,QAAQmB,EAAOJ,EAAE,GAAG,GAAIA,EAAE,GAAG,GAAM,CACtF,CAEO,SAASY,EAAiBtB,GAC7B,IAAIG,EAAGoB,EACP,OAAOpB,EAAI,CAAA,EAAIpB,EAAK,QAASA,EAAK,SAAS,SAAUc,GAAK,MAAMA,CAAE,IAAKd,EAAK,UAAWoB,EAAErC,OAAOC,UAAY,WAAc,OAAOmB,IAAO,EAAEiB,EAC1I,SAASpB,EAAKI,EAAGd,GAAK8B,EAAEhB,GAAKa,EAAEb,GAAK,SAAUC,GAAK,OAAQmC,GAAKA,GAAK,CAAE9B,MAAOW,EAAQJ,EAAEb,GAAGC,IAAKI,MAAM,GAAUnB,EAAIA,EAAEe,GAAKA,CAAE,EAAKf,CAAI,CAC1I,CAEO,SAASmD,EAAcxB,GAC1B,IAAKlC,OAAO0C,cAAe,MAAM,IAAIlB,UAAU,wCAC/C,IAAiCa,EAA7BD,EAAIF,EAAElC,OAAO0C,eACjB,OAAON,EAAIA,EAAEX,KAAKS,IAAMA,EAAqCD,EAASC,GAA2BG,EAAI,CAAE,EAAEpB,EAAK,QAASA,EAAK,SAAUA,EAAK,UAAWoB,EAAErC,OAAO0C,eAAiB,WAAc,OAAOtB,IAAK,EAAIiB,GAC9M,SAASpB,EAAKI,GAAKgB,EAAEhB,GAAKa,EAAEb,IAAM,SAAUC,GAAK,OAAO,IAAIuB,SAAQ,SAAUK,EAASG,IACvF,SAAgBH,EAASG,EAAQM,EAAGrC,GAAKuB,QAAQK,QAAQ5B,GAAG6B,MAAK,SAAS7B,GAAK4B,EAAQ,CAAEvB,MAAOL,EAAGI,KAAMiC,GAAK,GAAIN,EAAU,EADdC,CAAOJ,EAASG,GAA7B/B,EAAIY,EAAEb,GAAGC,IAA8BI,KAAMJ,EAAEK,MAAO,GAAM,CAAG,CAEpK,UC3PgBiC,IAEhB,CCCM,SAAUC,EAAaC,GAC3B,MAAqB,iBAANA,GAAwB,OAANA,GAA4B,mBAANA,CACzD,CFsTkD,mBAApBC,iBAAiCA,gBEpTxD,IAAMC,EAUPJ,EAEU,SAAAK,EAAgBC,EAAcC,GAC5C,IACEC,OAAOC,eAAeH,EAAI,OAAQ,CAChCvC,MAAOwC,EACPG,cAAc,GAEjB,CAAC,MAAAC,GAGD,CACH,CC1BA,IAAMC,EAAkB3B,QAClB4B,EAAsB5B,QAAQ6B,UAAUvB,KACxCwB,EAAwB9B,QAAQQ,OAAOuB,KAAKJ,GAG5C,SAAUK,EAAcC,GAI5B,OAAO,IAAIN,EAAgBM,EAC7B,CAGM,SAAUC,EAAuBpD,GACrC,OAAOkD,GAAW,SAAA3B,GAAW,OAAAA,EAAQvB,EAAR,GAC/B,CAGM,SAAUqD,EAA+BC,GAC7C,OAAON,EAAsBM,EAC/B,UAEgBC,EACdC,EACAC,EACAC,GAGA,OAAOZ,EAAoBhD,KAAK0D,EAASC,EAAaC,EACxD,UAKgBC,EACdH,EACAC,EACAC,GACAH,EACEA,EAAmBC,EAASC,EAAaC,QACzCE,EACAvB,EAEJ,CAEgB,SAAAwB,EAAmBL,EAAqBC,GACtDE,EAAYH,EAASC,EACvB,CAEgB,SAAAK,EAAcN,EAA2BE,GACvDC,EAAYH,OAASI,EAAWF,EAClC,UAEgBK,EACdP,EACAQ,EACAC,GACA,OAAOV,EAAmBC,EAASQ,EAAoBC,EACzD,CAEM,SAAUC,EAA0BV,GACxCD,EAAmBC,OAASI,EAAWvB,EACzC,CAEA,IAAI8B,EAAkD,SAAAC,GACpD,GAA8B,mBAAnBC,eACTF,EAAkBE,mBACb,CACL,IAAMC,EAAkBlB,OAAoBQ,GAC5CO,EAAkB,SAAAI,GAAM,OAAAhB,EAAmBe,EAAiBC,GAC7D,CACD,OAAOJ,EAAgBC,EACzB,WAIgBI,EAAmCC,EAAiCC,EAAMC,GACxF,GAAiB,mBAANF,EACT,MAAM,IAAI5E,UAAU,8BAEtB,OAAO+E,SAAS7B,UAAU/B,MAAMlB,KAAK2E,EAAGC,EAAGC,EAC7C,UAEgBE,EAAmCJ,EACAC,EACAC,GAIjD,IACE,OAAOvB,EAAoBoB,EAAYC,EAAGC,EAAGC,GAC9C,CAAC,MAAO3E,GACP,OAAOqD,EAAoBrD,EAC5B,CACH,CC5FA,IAaA8E,EAAA,WAME,SAAAA,IAHQrF,KAAOsF,QAAG,EACVtF,KAAKuF,MAAG,EAIdvF,KAAKwF,OAAS,CACZC,UAAW,GACXC,WAAOvB,GAETnE,KAAK2F,MAAQ3F,KAAKwF,OAIlBxF,KAAKsF,QAAU,EAEftF,KAAKuF,MAAQ,CACd,CAqGH,OAnGEvC,OAAAC,eAAIoC,EAAM/B,UAAA,SAAA,CAAVsC,IAAA,WACE,OAAO5F,KAAKuF,KACb,kCAMDF,EAAI/B,UAAA5C,KAAJ,SAAKmF,GACH,IAAMC,EAAU9F,KAAK2F,MACjBI,EAAUD,EAEmBE,QAA7BF,EAAQL,UAAUhF,SACpBsF,EAAU,CACRN,UAAW,GACXC,WAAOvB,IAMX2B,EAAQL,UAAU/E,KAAKmF,GACnBE,IAAYD,IACd9F,KAAK2F,MAAQI,EACbD,EAAQJ,MAAQK,KAEhB/F,KAAKuF,OAKTF,EAAA/B,UAAAnB,MAAA,WAGE,IAAM8D,EAAWjG,KAAKwF,OAClBU,EAAWD,EACTE,EAAYnG,KAAKsF,QACnBc,EAAYD,EAAY,EAEtBE,EAAWJ,EAASR,UACpBI,EAAUQ,EAASF,GAmBzB,OA7FyB,QA4ErBC,IAGFF,EAAWD,EAASP,MACpBU,EAAY,KAIZpG,KAAKuF,MACPvF,KAAKsF,QAAUc,EACXH,IAAaC,IACflG,KAAKwF,OAASU,GAIhBG,EAASF,QAAahC,EAEf0B,GAWTR,EAAO/B,UAAAgD,QAAP,SAAQ3B,GAIN,IAHA,IAAI1D,EAAIjB,KAAKsF,QACTiB,EAAOvG,KAAKwF,OACZa,EAAWE,EAAKd,YACbxE,IAAMoF,EAAS5F,aAAyB0D,IAAfoC,EAAKb,OAC/BzE,IAAMoF,EAAS5F,SAKjBQ,EAAI,EACoB,KAFxBoF,GADAE,EAAOA,EAAKb,OACID,WAEHhF,UAIfkE,EAAS0B,EAASpF,MAChBA,GAMNoE,EAAA/B,UAAAkD,KAAA,WAGE,IAAMC,EAAQzG,KAAKwF,OACbkB,EAAS1G,KAAKsF,QACpB,OAAOmB,EAAMhB,UAAUiB,IAE1BrB,CAAD,IC1IasB,EAAa/H,EAAO,kBACpBgI,EAAahI,EAAO,kBACpBiI,EAAcjI,EAAO,mBACrBkI,EAAYlI,EAAO,iBACnBmI,EAAenI,EAAO,oBCCnB,SAAAoI,EAAyCC,EAAiCC,GACxFD,EAAOE,qBAAuBD,EAC9BA,EAAOE,QAAUH,EAEK,aAAlBC,EAAOG,OACTC,EAAqCL,GACV,WAAlBC,EAAOG,OA2Dd,SAAyDJ,GAC7DK,EAAqCL,GACrCM,EAAkCN,EACpC,CA7DIO,CAA+CP,GAI/CQ,EAA+CR,EAAQC,EAAOQ,aAElE,CAKgB,SAAAC,EAAkCV,EAAmCpD,GAGnF,OAAO+D,GAFQX,EAAOE,qBAEctD,EACtC,CAEM,SAAUgE,EAAmCZ,GACjD,IAAMC,EAASD,EAAOE,qBAIA,aAAlBD,EAAOG,OACTS,EACEb,EACA,IAAI7G,UAAU,qFAiDJ,SAA0C6G,EAAmCpD,GAI3F4D,EAA+CR,EAAQpD,EACzD,CApDIkE,CACEd,EACA,IAAI7G,UAAU,qFAGlB8G,EAAOc,0BAA0BjB,KAEjCG,EAAOE,aAAUjD,EACjB8C,EAAOE,0BAAuBhD,CAChC,CAIM,SAAU8D,EAAoBlF,GAClC,OAAO,IAAI3C,UAAU,UAAY2C,EAAO,oCAC1C,CAIM,SAAUuE,EAAqCL,GACnDA,EAAOiB,eAAiBzE,GAAW,SAAC3B,EAASG,GAC3CgF,EAAOkB,uBAAyBrG,EAChCmF,EAAOmB,sBAAwBnG,CACjC,GACF,CAEgB,SAAAwF,EAA+CR,EAAmCpD,GAChGyD,EAAqCL,GACrCa,EAAiCb,EAAQpD,EAC3C,CAOgB,SAAAiE,EAAiCb,EAAmCpD,QAC7CM,IAAjC8C,EAAOmB,wBAIX3D,EAA0BwC,EAAOiB,gBACjCjB,EAAOmB,sBAAsBvE,GAC7BoD,EAAOkB,4BAAyBhE,EAChC8C,EAAOmB,2BAAwBjE,EACjC,CASM,SAAUoD,EAAkCN,QACV9C,IAAlC8C,EAAOkB,yBAIXlB,EAAOkB,4BAAuBhE,GAC9B8C,EAAOkB,4BAAyBhE,EAChC8C,EAAOmB,2BAAwBjE,EACjC,CClGA,IAAMkE,EAAyCC,OAAOC,UAAY,SAAU7F,GAC1E,MAAoB,iBAANA,GAAkB6F,SAAS7F,EAC3C,ECFM8F,EAA+BC,KAAKC,OAAS,SAAUxI,GAC3D,OAAOA,EAAI,EAAIuI,KAAKE,KAAKzI,GAAKuI,KAAKG,MAAM1I,EAC3C,ECGgB,SAAA2I,EAAiBC,EACAC,GAC/B,QAAY5E,IAAR2E,IALgB,iBADOpG,EAMYoG,IALM,mBAANpG,GAMrC,MAAM,IAAItC,UAAU,UAAG2I,EAAO,uBAP5B,IAAuBrG,CAS7B,CAKgB,SAAAsG,EAAetG,EAAYqG,GACzC,GAAiB,mBAANrG,EACT,MAAM,IAAItC,UAAU,UAAG2I,EAAO,uBAElC,CAOgB,SAAAE,EAAavG,EACAqG,GAC3B,IANI,SAAmBrG,GACvB,MAAqB,iBAANA,GAAwB,OAANA,GAA4B,mBAANA,CACzD,CAIOwG,CAASxG,GACZ,MAAM,IAAItC,UAAU,UAAG2I,EAAO,sBAElC,UAEgBI,EAA0BzG,EACA0G,EACAL,GACxC,QAAU5E,IAANzB,EACF,MAAM,IAAItC,UAAU,aAAArB,OAAaqK,EAA4B,qBAAArK,OAAAgK,EAAW,MAE5E,UAEgBM,EAAuB3G,EACA4G,EACAP,GACrC,QAAU5E,IAANzB,EACF,MAAM,IAAItC,UAAU,GAAArB,OAAGuK,EAAyB,qBAAAvK,OAAAgK,EAAW,MAE/D,CAGM,SAAUQ,EAA0BhJ,GACxC,OAAO+H,OAAO/H,EAChB,CAEA,SAASiJ,EAAmB9G,GAC1B,OAAa,IAANA,EAAU,EAAIA,CACvB,CAOgB,SAAA+G,EAAwClJ,EAAgBwI,GACtE,IACMW,EAAapB,OAAOqB,iBAEtBjH,EAAI4F,OAAO/H,GAGf,GAFAmC,EAAI8G,EAAmB9G,IAElB2F,EAAe3F,GAClB,MAAM,IAAItC,UAAU,UAAG2I,EAAO,4BAKhC,IAFArG,EAhBF,SAAqBA,GACnB,OAAO8G,EAAmBhB,EAAU9F,GACtC,CAcMkH,CAAYlH,IAVG,GAYGA,EAAIgH,EACxB,MAAM,IAAItJ,UAAU,GAAGrB,OAAAgK,EAA4C,sCAAAhK,OAblD,EAamE,QAAAA,OAAA2K,EAAuB,gBAG7G,OAAKrB,EAAe3F,IAAY,IAANA,EASnBA,EARE,CASX,CC3FgB,SAAAmH,EAAqBnH,EAAYqG,GAC/C,IAAKe,GAAiBpH,GACpB,MAAM,IAAItC,UAAU,UAAG2I,EAAO,6BAElC,CCwBM,SAAUgB,EAAsC7C,GACpD,OAAO,IAAI8C,GAA4B9C,EACzC,CAIgB,SAAA+C,EAAgC/C,EACAgD,GAI7ChD,EAAOE,QAA4C+C,cAAczJ,KAAKwJ,EACzE,UAEgBE,GAAoClD,EAA2BmD,EAAsB/J,GACnG,IAIM4J,EAJShD,EAAOE,QAIK+C,cAAchI,QACrC7B,EACF4J,EAAYI,cAEZJ,EAAYK,YAAYF,EAE5B,CAEM,SAAUG,GAAoCtD,GAClD,OAAQA,EAAOE,QAA2C+C,cAAc1J,MAC1E,CAEM,SAAUgK,GAA+BvD,GAC7C,IAAMD,EAASC,EAAOE,QAEtB,YAAejD,IAAX8C,KAICyD,GAA8BzD,EAKrC,CAiBA,aAAA+C,GAAA,WAYE,SAAAA,4BAAY9C,GAIV,GAHAiC,EAAuBjC,EAAQ,EAAG,+BAClC2C,EAAqB3C,EAAQ,mBAEzByD,GAAuBzD,GACzB,MAAM,IAAI9G,UAAU,+EAGtB4G,EAAsChH,KAAMkH,GAE5ClH,KAAKmK,cAAgB,IAAI9E,CAC1B,CA8EH,OAxEErC,OAAAC,eAAI+G,4BAAM1G,UAAA,SAAA,CAAVsC,IAAA,WACE,OAAK8E,GAA8B1K,MAI5BA,KAAKkI,eAHHtE,EAAoBgH,GAAiC,UAI/D,kCAKDZ,4BAAM1G,UAAAuH,OAAN,SAAOhH,GACL,YADK,IAAAA,IAAAA,OAAuBM,GACvBuG,GAA8B1K,WAIDmE,IAA9BnE,KAAKmH,qBACAvD,EAAoBqE,EAAoB,WAG1CN,EAAkC3H,KAAM6D,GAPtCD,EAAoBgH,GAAiC,YAehEZ,4BAAA1G,UAAAwH,KAAA,WACE,IAAKJ,GAA8B1K,MACjC,OAAO4D,EAAoBgH,GAAiC,SAG9D,QAAkCzG,IAA9BnE,KAAKmH,qBACP,OAAOvD,EAAoBqE,EAAoB,cAGjD,IAAI8C,EACAC,EACEjH,EAAUN,GAA+C,SAAC3B,EAASG,GACvE8I,EAAiBjJ,EACjBkJ,EAAgB/I,CAClB,IAOA,OADAgJ,GAAgCjL,KALI,CAClCuK,YAAa,SAAAF,GAAS,OAAAU,EAAe,CAAExK,MAAO8J,EAAO/J,MAAM,GAAQ,EACnEgK,YAAa,WAAM,OAAAS,EAAe,CAAExK,WAAO4D,EAAW7D,MAAM,GAAO,EACnE4K,YAAa,SAAAvK,GAAK,OAAAqK,EAAcrK,EAAE,IAG7BoD,GAYTiG,4BAAA1G,UAAA6H,YAAA,WACE,IAAKT,GAA8B1K,MACjC,MAAM4K,GAAiC,oBAGPzG,IAA9BnE,KAAKmH,sBAwDP,SAA6CF,GACjDY,EAAmCZ,GACnC,IAAMtG,EAAI,IAAIP,UAAU,uBACxBgL,GAA6CnE,EAAQtG,EACvD,CAxDI0K,CAAmCrL,OAEtCgK,2BAAD,IAoBM,SAAUU,GAAuChI,GACrD,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,kBAItCA,aAAasH,GACtB,CAEgB,SAAAiB,GAAmChE,EACAiD,GACjD,IAAMhD,EAASD,EAAOE,qBAItBD,EAAOqE,YAAa,EAEE,WAAlBrE,EAAOG,OACT6C,EAAYI,cACe,YAAlBpD,EAAOG,OAChB6C,EAAYgB,YAAYhE,EAAOQ,cAG/BR,EAAOc,0BAA0BlB,GAAWoD,EAEhD,CAQgB,SAAAkB,GAA6CnE,EAAqCtG,GAChG,IAAM6K,EAAevE,EAAOkD,cAC5BlD,EAAOkD,cAAgB,IAAI9E,EAC3BmG,EAAalF,SAAQ,SAAA4D,GACnBA,EAAYgB,YAAYvK,EAC1B,GACF,CAIA,SAASiK,GAAiC7H,GACxC,OAAO,IAAI3C,UACT,gDAAyC2C,EAAI,sDACjD,CCtPM,SAAU0I,GAAqCpF,GAGnD,OAAOA,EAASqF,OAClB,CAEM,SAAUC,GAAmBC,EACAC,EACAC,EACAC,EACA9L,GACjC,IAAI+L,WAAWJ,GAAMK,IAAI,IAAID,WAAWF,EAAKC,EAAW9L,GAAI4L,EAC9D,CDuKA7I,OAAOkJ,iBAAiBlC,GAA4B1G,UAAW,CAC7DuH,OAAQ,CAAEsB,YAAY,GACtBrB,KAAM,CAAEqB,YAAY,GACpBhB,YAAa,CAAEgB,YAAY,GAC3BC,OAAQ,CAAED,YAAY,KAExBtJ,EAAgBmH,GAA4B1G,UAAUuH,OAAQ,UAC9DhI,EAAgBmH,GAA4B1G,UAAUwH,KAAM,QAC5DjI,EAAgBmH,GAA4B1G,UAAU6H,YAAa,eACjC,iBAAvBvM,EAAOyN,aAChBrJ,OAAOC,eAAe+G,GAA4B1G,UAAW1E,EAAOyN,YAAa,CAC/E9L,MAAO,8BACP2C,cAAc,ICjLX,IAAIoJ,GAAsB,SAACC,GAShC,OAPED,GADwB,mBAAfC,EAAEC,SACW,SAAAC,GAAU,OAAAA,EAAOD,YACH,mBAApBE,gBACM,SAAAD,GAAU,OAAAC,gBAAgBD,EAAQ,CAAED,SAAU,CAACC,IAAU,EAGzD,SAAAA,GAAU,OAAAA,CAAM,GAEbF,EAC7B,EAMWI,GAAmB,SAACJ,GAO7B,OALEI,GADwB,kBAAfJ,EAAEK,SACQ,SAAAH,GAAU,OAAAA,EAAOG,QAAP,EAGV,SAAAH,GAAU,OAAsB,IAAtBA,EAAOI,aAEdN,EAC1B,WAEgBO,GAAiBL,EAAqBM,EAAeC,GAGnE,GAAIP,EAAOf,MACT,OAAOe,EAAOf,MAAMqB,EAAOC,GAE7B,IAAMvM,EAASuM,EAAMD,EACfrB,EAAQ,IAAIuB,YAAYxM,GAE9B,OADAkL,GAAmBD,EAAO,EAAGe,EAAQM,EAAOtM,GACrCiL,CACT,CAMgB,SAAAwB,GAAsCC,EAAaC,GACjE,IAAMC,EAAOF,EAASC,GACtB,GAAIC,QAAJ,CAGA,GAAoB,mBAATA,EACT,MAAM,IAAIjN,UAAU,GAAGrB,OAAAuO,OAAOF,GAAyB,uBAEzD,OAAOC,CAJN,CAKH,CAkCO,OAAME,GAEyB,QADpCC,WAAArK,GAAAvE,EAAO0C,+BACG,QAAVmM,GAAA7O,EAAO8O,WAAG,IAAAD,QAAA,EAAAA,GAAApN,KAAAzB,EAAG,+BAAuB,IAAA4O,GAAAA,GACpC,kBAeF,SAASG,GACP7E,EACA8E,EACAC,GAGA,QAJA,IAAAD,IAAAA,EAAa,aAIEzJ,IAAX0J,EACF,GAAa,UAATD,GAEF,QAAezJ,KADf0J,EAASX,GAAUpE,EAAyByE,KAI1C,OAhDF,SAAyCO,SAKvCC,IAAY5K,EAAA,CAAA,GACfvE,EAAOC,UAAW,WAAM,OAAAiP,EAAmBjP,QAAQ,KAGhDyC,EAAiB,0FACd,KAAA,EAAA,MAAA,CAAA,EAAAT,EAAOuB,EAAAE,EAAAyL,MAAP,KAAA,kCAAA5K,EAAmB1D,iBAA1B,MAA2B,CAAA,EAAA0D,EAAA1D,cAC5B,CAFkB,GAKnB,MAAO,CAAEZ,SAAUyC,EAAe0M,WADf1M,EAAc1B,KACaU,MAAM,EACtD,CAiCe2N,CADoBN,GAAY7E,EAAoB,OADxCoE,GAAUpE,EAAoBlK,EAAOC,iBAK1DgP,EAASX,GAAUpE,EAAoBlK,EAAOC,UAGlD,QAAesF,IAAX0J,EACF,MAAM,IAAIzN,UAAU,8BAEtB,IAAMvB,EAAWkG,EAAY8I,EAAQ/E,EAAK,IAC1C,IAAKrG,EAAa5D,GAChB,MAAM,IAAIuB,UAAU,6CAGtB,MAAO,CAAEvB,SAAQA,EAAEmP,WADAnP,EAASe,KACGU,MAAM,EACvC,CCzJO,IAAM4N,KAAsB/K,GAAA,CAAA,GAGhCoK,IAAD,WACE,OAAOvN,IACR,MAEHgD,OAAOC,eAAeiL,GAAwBX,GAAqB,CAAEpB,YAAY,ICqBjF,IAAAgC,GAAA,WAME,SAAYA,EAAAlH,EAAwCmH,GAH5CpO,KAAeqO,qBAA4DlK,EAC3EnE,KAAWsO,aAAG,EAGpBtO,KAAKoH,QAAUH,EACfjH,KAAKuO,eAAiBH,CACvB,CA0EH,OAxEED,EAAA7K,UAAA1D,KAAA,WAAA,IAMC4O,EAAAxO,KALOyO,EAAY,WAAM,OAAAD,EAAKE,YAAL,EAIxB,OAHA1O,KAAKqO,gBAAkBrO,KAAKqO,gBAC1B/J,EAAqBtE,KAAKqO,gBAAiBI,EAAWA,GACtDA,IACKzO,KAAKqO,iBAGdF,EAAM7K,UAAAvD,OAAN,SAAOQ,GAAP,IAKCiO,EAAAxO,KAJO2O,EAAc,WAAM,OAAAH,EAAKI,aAAarO,IAC5C,OAAOP,KAAKqO,gBACV/J,EAAqBtE,KAAKqO,gBAAiBM,EAAaA,GACxDA,KAGIR,EAAA7K,UAAAoL,WAAR,WAAA,IAoCCF,EAAAxO,KAnCC,GAAIA,KAAKsO,YACP,OAAO7M,QAAQK,QAAQ,CAAEvB,WAAO4D,EAAW7D,MAAM,IAGnD,IAGIyK,EACAC,EAJE/D,EAASjH,KAAKoH,QAKdrD,EAAUN,GAA+C,SAAC3B,EAASG,GACvE8I,EAAiBjJ,EACjBkJ,EAAgB/I,CAClB,IAsBA,OADAgJ,GAAgChE,EApBI,CAClCsD,YAAa,SAAAF,GACXmE,EAAKH,qBAAkBlK,EAGvBS,GAAe,WAAM,OAAAmG,EAAe,CAAExK,MAAO8J,EAAO/J,MAAM,GAArC,GACtB,EACDgK,YAAa,WACXkE,EAAKH,qBAAkBlK,EACvBqK,EAAKF,aAAc,EACnBzG,EAAmCZ,GACnC8D,EAAe,CAAExK,WAAO4D,EAAW7D,MAAM,GAC1C,EACD4K,YAAa,SAAArH,GACX2K,EAAKH,qBAAkBlK,EACvBqK,EAAKF,aAAc,EACnBzG,EAAmCZ,GACnC+D,EAAcnH,EACf,IAGIE,GAGDoK,EAAY7K,UAAAsL,aAApB,SAAqBrO,GACnB,GAAIP,KAAKsO,YACP,OAAO7M,QAAQK,QAAQ,CAAEvB,MAAKA,EAAED,MAAM,IAExCN,KAAKsO,aAAc,EAEnB,IAAMrH,EAASjH,KAAKoH,QAIpB,IAAKpH,KAAKuO,eAAgB,CACxB,IAAMM,EAASlH,EAAkCV,EAAQ1G,GAEzD,OADAsH,EAAmCZ,GAC5B3C,EAAqBuK,GAAQ,WAAM,OAAGtO,QAAOD,MAAM,EAAhB,GAC3C,CAGD,OADAuH,EAAmCZ,GAC5BtD,EAAoB,CAAEpD,MAAKA,EAAED,MAAM,KAE7C6N,CAAD,IAWMW,GAAiF,CACrFlP,KAAI,WACF,OAAKmP,GAA8B/O,MAG5BA,KAAKgP,mBAAmBpP,OAFtBgE,EAAoBqL,GAAuC,QAGrE,EAEDlP,gBAAuDQ,GACrD,OAAKwO,GAA8B/O,MAG5BA,KAAKgP,mBAAmBjP,OAAOQ,GAF7BqD,EAAoBqL,GAAuC,UAGrE,GAeH,SAASF,GAAuCrM,GAC9C,IAAKD,EAAaC,GAChB,OAAO,EAGT,IAAKM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,sBAC3C,OAAO,EAGT,IAEE,OAAQA,EAA+CsM,8BACrDb,EACH,CAAC,MAAAhL,GACA,OAAO,CACR,CACH,CAIA,SAAS8L,GAAuClM,GAC9C,OAAO,IAAI3C,UAAU,sCAA+B2C,EAAI,qDAC1D,CAnCAC,OAAOkM,eAAeJ,GAAsCZ,IC3I5D,IAAMiB,GAAmC7G,OAAO8G,OAAS,SAAU1M,GAEjE,OAAOA,GAAMA,CACf,ECcM,SAAU2M,GAAkB9C,GAChC,IAAME,EAASK,GAAiBP,EAAEE,OAAQF,EAAE+C,WAAY/C,EAAE+C,WAAa/C,EAAEM,YACzE,OAAO,IAAIb,WAAWS,EACxB,CCTM,SAAU8C,GAAgBC,GAI9B,IAAMC,EAAOD,EAAUE,OAAOvN,QAM9B,OALAqN,EAAUG,iBAAmBF,EAAKG,KAC9BJ,EAAUG,gBAAkB,IAC9BH,EAAUG,gBAAkB,GAGvBF,EAAKlP,KACd,UAEgBsP,GAAwBL,EAAyCjP,EAAUqP,GAGzF,GDzBiB,iBADiB1P,EC0BT0P,IDrBrBT,GAAYjP,IAIZA,EAAI,GCiB0B0P,IAASE,IACzC,MAAM,IAAIC,WAAW,wDD3BnB,IAA8B7P,EC8BlCsP,EAAUE,OAAOhP,KAAK,CAAEH,MAAKA,EAAEqP,KAAIA,IACnCJ,EAAUG,iBAAmBC,CAC/B,CAUM,SAAUI,GAAcR,GAG5BA,EAAUE,OAAS,IAAIrK,EACvBmK,EAAUG,gBAAkB,CAC9B,CCxBA,SAASM,GAAsBC,GAC7B,OAAOA,IAASC,QAClB,CCoBA,IAAAC,GAAA,WAME,SAAAA,4BACE,MAAM,IAAIhQ,UAAU,sBACrB,CAsEH,OAjEE4C,OAAAC,eAAImN,0BAAI9M,UAAA,OAAA,CAARsC,IAAA,WACE,IAAKyK,GAA4BrQ,MAC/B,MAAMsQ,GAA+B,QAGvC,OAAOtQ,KAAKuQ,KACb,kCAUDH,0BAAO9M,UAAAkN,QAAP,SAAQC,GACN,IAAKJ,GAA4BrQ,MAC/B,MAAMsQ,GAA+B,WAKvC,GAHAnH,EAAuBsH,EAAc,EAAG,WACxCA,EAAehH,EAAwCgH,EAAc,wBAEhBtM,IAAjDnE,KAAK0Q,wCACP,MAAM,IAAItQ,UAAU,0CAGtB,GAAIuM,GAAiB3M,KAAKuQ,MAAO9D,QAC/B,MAAM,IAAIrM,UAAU,mFAMtBuQ,GAAoC3Q,KAAK0Q,wCAAyCD,IAWpFL,0BAAkB9M,UAAAsN,mBAAlB,SAAmBC,GACjB,IAAKR,GAA4BrQ,MAC/B,MAAMsQ,GAA+B,sBAIvC,GAFAnH,EAAuB0H,EAAM,EAAG,uBAE3B5D,YAAY6D,OAAOD,GACtB,MAAM,IAAIzQ,UAAU,gDAGtB,QAAqD+D,IAAjDnE,KAAK0Q,wCACP,MAAM,IAAItQ,UAAU,0CAGtB,GAAIuM,GAAiBkE,EAAKpE,QACxB,MAAM,IAAIrM,UAAU,iFAGtB2Q,GAA+C/Q,KAAK0Q,wCAAyCG,IAEhGT,yBAAD,IAEApN,OAAOkJ,iBAAiBkE,GAA0B9M,UAAW,CAC3DkN,QAAS,CAAErE,YAAY,GACvByE,mBAAoB,CAAEzE,YAAY,GAClC0E,KAAM,CAAE1E,YAAY,KAEtBtJ,EAAgBuN,GAA0B9M,UAAUkN,QAAS,WAC7D3N,EAAgBuN,GAA0B9M,UAAUsN,mBAAoB,sBACtC,iBAAvBhS,EAAOyN,aAChBrJ,OAAOC,eAAemN,GAA0B9M,UAAW1E,EAAOyN,YAAa,CAC7E9L,MAAO,4BACP2C,cAAc,IA2ClB,IAAA8N,GAAA,WA4BE,SAAAA,+BACE,MAAM,IAAI5Q,UAAU,sBACrB,CAwJH,OAnJE4C,OAAAC,eAAI+N,6BAAW1N,UAAA,cAAA,CAAfsC,IAAA,WACE,IAAKqL,GAA+BjR,MAClC,MAAMkR,GAAwC,eAGhD,OAAOC,GAA2CnR,KACnD,kCAMDgD,OAAAC,eAAI+N,6BAAW1N,UAAA,cAAA,CAAfsC,IAAA,WACE,IAAKqL,GAA+BjR,MAClC,MAAMkR,GAAwC,eAGhD,OAAOE,GAA2CpR,KACnD,kCAMDgR,6BAAA1N,UAAA+N,MAAA,WACE,IAAKJ,GAA+BjR,MAClC,MAAMkR,GAAwC,SAGhD,GAAIlR,KAAKsR,gBACP,MAAM,IAAIlR,UAAU,8DAGtB,IAAMmR,EAAQvR,KAAKwR,8BAA8BnK,OACjD,GAAc,aAAVkK,EACF,MAAM,IAAInR,UAAU,yBAAkBmR,EAAK,8DAG7CE,GAAkCzR,OAQpCgR,6BAAO1N,UAAAoO,QAAP,SAAQrH,GACN,IAAK4G,GAA+BjR,MAClC,MAAMkR,GAAwC,WAIhD,GADA/H,EAAuBkB,EAAO,EAAG,YAC5B4C,YAAY6D,OAAOzG,GACtB,MAAM,IAAIjK,UAAU,sCAEtB,GAAyB,IAArBiK,EAAMwC,WACR,MAAM,IAAIzM,UAAU,uCAEtB,GAAgC,IAA5BiK,EAAMoC,OAAOI,WACf,MAAM,IAAIzM,UAAU,gDAGtB,GAAIJ,KAAKsR,gBACP,MAAM,IAAIlR,UAAU,gCAGtB,IAAMmR,EAAQvR,KAAKwR,8BAA8BnK,OACjD,GAAc,aAAVkK,EACF,MAAM,IAAInR,UAAU,yBAAkBmR,EAAK,mEAG7CI,GAAoC3R,KAAMqK,IAM5C2G,6BAAK1N,UAAAsO,MAAL,SAAMjR,GACJ,QADI,IAAAA,IAAAA,OAAkBwD,IACjB8M,GAA+BjR,MAClC,MAAMkR,GAAwC,SAGhDW,GAAkC7R,KAAMW,IAI1CqQ,6BAAA1N,UAACuD,GAAD,SAAchD,GACZiO,GAAkD9R,MAElDgQ,GAAWhQ,MAEX,IAAM6O,EAAS7O,KAAK+R,iBAAiBlO,GAErC,OADAmO,GAA4ChS,MACrC6O,GAITmC,6BAAA1N,UAACwD,GAAD,SAAYoD,GACV,IAAMhD,EAASlH,KAAKwR,8BAGpB,GAAIxR,KAAK2P,gBAAkB,EAGzBsC,GAAqDjS,KAAMkK,OAH7D,CAOA,IAAMgI,EAAwBlS,KAAKmS,uBACnC,QAA8BhO,IAA1B+N,EAAqC,CACvC,IAAIzF,SACJ,IACEA,EAAS,IAAIQ,YAAYiF,EAC1B,CAAC,MAAOE,GAEP,YADAlI,EAAYgB,YAAYkH,EAEzB,CAED,IAAMC,EAAgD,CACpD5F,OAAMA,EACN6F,iBAAkBJ,EAClB5C,WAAY,EACZzC,WAAYqF,EACZK,YAAa,EACbC,YAAa,EACbC,YAAa,EACbC,gBAAiB1G,WACjB2G,WAAY,WAGd3S,KAAK4S,kBAAkBlS,KAAK2R,EAC7B,CAEDpI,EAA6B/C,EAAQgD,GACrC2I,GAA6C7S,KA5B5C,GAgCHgR,6BAAC1N,UAAAyD,GAAD,WACE,GAAI/G,KAAK4S,kBAAkBnS,OAAS,EAAG,CACrC,IAAMqS,EAAgB9S,KAAK4S,kBAAkBpM,OAC7CsM,EAAcH,WAAa,OAE3B3S,KAAK4S,kBAAoB,IAAIvN,EAC7BrF,KAAK4S,kBAAkBlS,KAAKoS,EAC7B,GAEJ9B,4BAAD,IAqBM,SAAUC,GAA+BvO,GAC7C,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,kCAItCA,aAAasO,GACtB,CAEA,SAASX,GAA4B3N,GACnC,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,4CAItCA,aAAa0N,GACtB,CAEA,SAASyC,GAA6CE,GACpD,IAAMC,EAiYR,SAAoDD,GAClD,IAAM7L,EAAS6L,EAAWvB,8BAE1B,GAAsB,aAAlBtK,EAAOG,OACT,OAAO,EAGT,GAAI0L,EAAWzB,gBACb,OAAO,EAGT,IAAKyB,EAAWE,SACd,OAAO,EAGT,GAAIxI,GAA+BvD,IAAWsD,GAAiCtD,GAAU,EACvF,OAAO,EAGT,GAAIgM,GAA4BhM,IAAWiM,GAAqCjM,GAAU,EACxF,OAAO,EAGT,IAAMkM,EAAchC,GAA2C2B,GAE/D,GAAIK,EAAe,EACjB,OAAO,EAGT,OAAO,CACT,CA/ZqBC,CAA2CN,GACzDC,IAIDD,EAAWO,SACbP,EAAWQ,YAAa,GAM1BR,EAAWO,UAAW,EAItBpP,EADoB6O,EAAWS,kBAG7B,WAQE,OAPAT,EAAWO,UAAW,EAElBP,EAAWQ,aACbR,EAAWQ,YAAa,EACxBV,GAA6CE,IAGxC,IACR,IACD,SAAApS,GAEE,OADAkR,GAAkCkB,EAAYpS,GACvC,IACT,KAEJ,CAEA,SAASmR,GAAkDiB,GACzDU,GAAkDV,GAClDA,EAAWH,kBAAoB,IAAIvN,CACrC,CAEA,SAASqO,GACPxM,EACAmL,GAKA,IAAI/R,GAAO,EACW,WAAlB4G,EAAOG,SAET/G,GAAO,GAGT,IAAMqT,EAAaC,GAAyDvB,GACtC,YAAlCA,EAAmBM,WACrBvI,GAAiClD,EAAQyM,EAAgDrT,YCxZxC4G,EACAmD,EACA/J,GACnD,IAAM2G,EAASC,EAAOE,QAIhByM,EAAkB5M,EAAO6M,kBAAkB3R,QAC7C7B,EACFuT,EAAgBvJ,YAAYD,GAE5BwJ,EAAgBtJ,YAAYF,EAEhC,CD8YI0J,CAAqC7M,EAAQyM,EAAYrT,EAE7D,CAEA,SAASsT,GACPvB,GAEA,IAAME,EAAcF,EAAmBE,YACjCE,EAAcJ,EAAmBI,YAKvC,OAAO,IAAIJ,EAAmBK,gBAC5BL,EAAmB5F,OAAQ4F,EAAmB/C,WAAYiD,EAAcE,EAC5E,CAEA,SAASuB,GAAgDjB,EACAtG,EACA6C,EACAzC,GACvDkG,EAAWrD,OAAOhP,KAAK,CAAE+L,OAAMA,EAAE6C,aAAYzC,WAAUA,IACvDkG,EAAWpD,iBAAmB9C,CAChC,CAEA,SAASoH,GAAsDlB,EACAtG,EACA6C,EACAzC,GAC7D,IAAIqH,EACJ,IACEA,EAAcpH,GAAiBL,EAAQ6C,EAAYA,EAAazC,EACjE,CAAC,MAAOsH,GAEP,MADAtC,GAAkCkB,EAAYoB,GACxCA,CACP,CACDH,GAAgDjB,EAAYmB,EAAa,EAAGrH,EAC9E,CAEA,SAASuH,GAA2DrB,EACAsB,GAE9DA,EAAgB9B,YAAc,GAChC0B,GACElB,EACAsB,EAAgB5H,OAChB4H,EAAgB/E,WAChB+E,EAAgB9B,aAGpB+B,GAAiDvB,EACnD,CAEA,SAASwB,GAA4DxB,EACAV,GACnE,IAAMmC,EAAiB/L,KAAKgM,IAAI1B,EAAWpD,gBACX0C,EAAmBxF,WAAawF,EAAmBE,aAC7EmC,EAAiBrC,EAAmBE,YAAciC,EAEpDG,EAA4BH,EAC5BI,GAAQ,EAGNC,EAAkBH,EADDA,EAAiBrC,EAAmBI,YAIvDoC,GAAmBxC,EAAmBG,cACxCmC,EAA4BE,EAAkBxC,EAAmBE,YACjEqC,GAAQ,GAKV,IAFA,IAAME,EAAQ/B,EAAWrD,OAElBiF,EAA4B,GAAG,CACpC,IAAMI,EAAcD,EAAMtO,OAEpBwO,EAAcvM,KAAKgM,IAAIE,EAA2BI,EAAYlI,YAE9DoI,EAAY5C,EAAmB/C,WAAa+C,EAAmBE,YACrE5G,GAAmB0G,EAAmB5F,OAAQwI,EAAWF,EAAYtI,OAAQsI,EAAYzF,WAAY0F,GAEjGD,EAAYlI,aAAemI,EAC7BF,EAAM3S,SAEN4S,EAAYzF,YAAc0F,EAC1BD,EAAYlI,YAAcmI,GAE5BjC,EAAWpD,iBAAmBqF,EAE9BE,GAAuDnC,EAAYiC,EAAa3C,GAEhFsC,GAA6BK,CAC9B,CAQD,OAAOJ,CACT,CAEA,SAASM,GAAuDnC,EACAnD,EACAyC,GAG9DA,EAAmBE,aAAe3C,CACpC,CAEA,SAASuF,GAA6CpC,GAGjB,IAA/BA,EAAWpD,iBAAyBoD,EAAWzB,iBACjDU,GAA4Ce,GAC5CqC,GAAoBrC,EAAWvB,gCAE/BqB,GAA6CE,EAEjD,CAEA,SAASU,GAAkDV,GACzB,OAA5BA,EAAWsC,eAIftC,EAAWsC,aAAa3E,6CAA0CvM,EAClE4O,EAAWsC,aAAa9E,MAAQ,KAChCwC,EAAWsC,aAAe,KAC5B,CAEA,SAASC,GAAiEvC,GAGxE,KAAOA,EAAWH,kBAAkBnS,OAAS,GAAG,CAC9C,GAAmC,IAA/BsS,EAAWpD,gBACb,OAGF,IAAM0C,EAAqBU,EAAWH,kBAAkBpM,OAGpD+N,GAA4DxB,EAAYV,KAC1EiC,GAAiDvB,GAEjDW,GACEX,EAAWvB,8BACXa,GAGL,CACH,CAcM,SAAUkD,GACdxC,EACAlC,EACA4D,EACAZ,GAEA,IAWIpH,EAXEvF,EAAS6L,EAAWvB,8BAEpBtB,EAAOW,EAAK2E,YACZ/C,EDhmBF,SAAgEvC,GACpE,OAAID,GAAsBC,GACjB,EAEDA,EAA0CuF,iBACpD,CC2lBsBC,CAA2BxF,GAEvCZ,EAA2BuB,EAAIvB,WAAnBzC,EAAegE,EAAIhE,WAEjC2F,EAAciC,EAAMhC,EAK1B,IACEhG,EAASH,GAAoBuE,EAAKpE,OACnC,CAAC,MAAO9L,GAEP,YADAkT,EAAgB3I,YAAYvK,EAE7B,CAED,IAAM0R,EAAgD,CACpD5F,OAAMA,EACN6F,iBAAkB7F,EAAOI,WACzByC,WAAUA,EACVzC,WAAUA,EACV0F,YAAa,EACbC,YAAWA,EACXC,YAAWA,EACXC,gBAAiBxC,EACjByC,WAAY,QAGd,GAAII,EAAWH,kBAAkBnS,OAAS,EAQxC,OAPAsS,EAAWH,kBAAkBlS,KAAK2R,QAMlCsD,GAAiCzO,EAAQ2M,GAI3C,GAAsB,WAAlB3M,EAAOG,OAAX,CAMA,GAAI0L,EAAWpD,gBAAkB,EAAG,CAClC,GAAI4E,GAA4DxB,EAAYV,GAAqB,CAC/F,IAAMsB,EAAaC,GAAyDvB,GAK5E,OAHA8C,GAA6CpC,QAE7Cc,EAAgBtJ,YAAYoJ,EAE7B,CAED,GAAIZ,EAAWzB,gBAAiB,CAC9B,IAAM3Q,EAAI,IAAIP,UAAU,2DAIxB,OAHAyR,GAAkCkB,EAAYpS,QAE9CkT,EAAgB3I,YAAYvK,EAE7B,CACF,CAEDoS,EAAWH,kBAAkBlS,KAAK2R,GAElCsD,GAAoCzO,EAAQ2M,GAC5ChB,GAA6CE,EAxB5C,KAJD,CACE,IAAM6C,EAAY,IAAI1F,EAAKmC,EAAmB5F,OAAQ4F,EAAmB/C,WAAY,GACrFuE,EAAgBvJ,YAAYsL,EAE7B,CAyBH,CAyDA,SAASC,GAA4C9C,EAA0CtC,GAC7F,IAAM4D,EAAkBtB,EAAWH,kBAAkBpM,OAGrDiN,GAAkDV,GAGpC,WADAA,EAAWvB,8BAA8BnK,OA7DzD,SAA0D0L,EACAsB,GAGrB,SAA/BA,EAAgB1B,YAClB2B,GAAiDvB,GAGnD,IAAM7L,EAAS6L,EAAWvB,8BAC1B,GAAI0B,GAA4BhM,GAC9B,KAAOiM,GAAqCjM,GAAU,GAEpDwM,GAAqDxM,EAD1BoN,GAAiDvB,GAIlF,CAiDI+C,CAAiD/C,EAAYsB,GA/CjE,SAA4DtB,EACAtC,EACA4B,GAK1D,GAFA6C,GAAuDnC,EAAYtC,EAAc4B,GAE3C,SAAlCA,EAAmBM,WAGrB,OAFAyB,GAA2DrB,EAAYV,QACvEiD,GAAiEvC,GAInE,KAAIV,EAAmBE,YAAcF,EAAmBG,aAAxD,CAMA8B,GAAiDvB,GAEjD,IAAMgD,EAAgB1D,EAAmBE,YAAcF,EAAmBI,YAC1E,GAAIsD,EAAgB,EAAG,CACrB,IAAM/I,EAAMqF,EAAmB/C,WAAa+C,EAAmBE,YAC/D0B,GACElB,EACAV,EAAmB5F,OACnBO,EAAM+I,EACNA,EAEH,CAED1D,EAAmBE,aAAewD,EAClCrC,GAAqDX,EAAWvB,8BAA+Ba,GAE/FiD,GAAiEvC,EAlBhE,CAmBH,CAeIiD,CAAmDjD,EAAYtC,EAAc4D,GAG/ExB,GAA6CE,EAC/C,CAEA,SAASuB,GACPvB,GAIA,OADmBA,EAAWH,kBAAkBzQ,OAElD,CAkCA,SAAS6P,GAA4Ce,GACnDA,EAAWS,oBAAiBrP,EAC5B4O,EAAWhB,sBAAmB5N,CAChC,CAIM,SAAUsN,GAAkCsB,GAChD,IAAM7L,EAAS6L,EAAWvB,8BAE1B,IAAIuB,EAAWzB,iBAAqC,aAAlBpK,EAAOG,OAIzC,GAAI0L,EAAWpD,gBAAkB,EAC/BoD,EAAWzB,iBAAkB,MAD/B,CAMA,GAAIyB,EAAWH,kBAAkBnS,OAAS,EAAG,CAC3C,IAAMwV,EAAuBlD,EAAWH,kBAAkBpM,OAC1D,GAAIyP,EAAqB1D,YAAc0D,EAAqBxD,aAAgB,EAAG,CAC7E,IAAM9R,EAAI,IAAIP,UAAU,2DAGxB,MAFAyR,GAAkCkB,EAAYpS,GAExCA,CACP,CACF,CAEDqR,GAA4Ce,GAC5CqC,GAAoBlO,EAbnB,CAcH,CAEgB,SAAAyK,GACdoB,EACA1I,GAEA,IAAMnD,EAAS6L,EAAWvB,8BAE1B,IAAIuB,EAAWzB,iBAAqC,aAAlBpK,EAAOG,OAAzC,CAIQ,IAAAoF,EAAmCpC,EAAKoC,OAAhC6C,EAA2BjF,EAAKiF,WAApBzC,EAAexC,aAC3C,GAAIsC,GAAiBF,GACnB,MAAM,IAAIrM,UAAU,wDAEtB,IAAM8V,EAAoB5J,GAAoBG,GAE9C,GAAIsG,EAAWH,kBAAkBnS,OAAS,EAAG,CAC3C,IAAMwV,EAAuBlD,EAAWH,kBAAkBpM,OAC1D,GAAImG,GAAiBsJ,EAAqBxJ,QACxC,MAAM,IAAIrM,UACR,8FAGJqT,GAAkDV,GAClDkD,EAAqBxJ,OAASH,GAAoB2J,EAAqBxJ,QAC/B,SAApCwJ,EAAqBtD,YACvByB,GAA2DrB,EAAYkD,EAE1E,CAED,GAAIxL,GAA+BvD,GAEjC,GA/QJ,SAAmE6L,GAGjE,IAFA,IAAM9L,EAAS8L,EAAWvB,8BAA8BpK,QAEjDH,EAAOkD,cAAc1J,OAAS,GAAG,CACtC,GAAmC,IAA/BsS,EAAWpD,gBACb,OAGFsC,GAAqDc,EADjC9L,EAAOkD,cAAchI,QAE1C,CACH,CAoQIgU,CAA0DpD,GACT,IAA7CvI,GAAiCtD,GAEnC8M,GAAgDjB,EAAYmD,EAAmB5G,EAAYzC,QAGvFkG,EAAWH,kBAAkBnS,OAAS,GAExC6T,GAAiDvB,GAGnD3I,GAAiClD,EADT,IAAI8E,WAAWkK,EAAmB5G,EAAYzC,IACa,QAE5EqG,GAA4BhM,IAErC8M,GAAgDjB,EAAYmD,EAAmB5G,EAAYzC,GAC3FyI,GAAiEvC,IAGjEiB,GAAgDjB,EAAYmD,EAAmB5G,EAAYzC,GAG7FgG,GAA6CE,EA7C5C,CA8CH,CAEgB,SAAAlB,GAAkCkB,EAA0CpS,GAC1F,IAAMuG,EAAS6L,EAAWvB,8BAEJ,aAAlBtK,EAAOG,SAIXyK,GAAkDiB,GAElD/C,GAAW+C,GACXf,GAA4Ce,GAC5CqD,GAAoBlP,EAAQvG,GAC9B,CAEgB,SAAAsR,GACdc,EACA7I,GAIA,IAAMmM,EAAQtD,EAAWrD,OAAOvN,QAChC4Q,EAAWpD,iBAAmB0G,EAAMxJ,WAEpCsI,GAA6CpC,GAE7C,IAAMlC,EAAO,IAAI7E,WAAWqK,EAAM5J,OAAQ4J,EAAM/G,WAAY+G,EAAMxJ,YAClE3C,EAAYK,YAAYsG,EAC1B,CAEM,SAAUM,GACd4B,GAEA,GAAgC,OAA5BA,EAAWsC,cAAyBtC,EAAWH,kBAAkBnS,OAAS,EAAG,CAC/E,IAAM4T,EAAkBtB,EAAWH,kBAAkBpM,OAC/CqK,EAAO,IAAI7E,WAAWqI,EAAgB5H,OAChB4H,EAAgB/E,WAAa+E,EAAgB9B,YAC7C8B,EAAgBxH,WAAawH,EAAgB9B,aAEnE+D,EAAyCtT,OAAOuT,OAAOnG,GAA0B9M,YA+K3F,SAAwCkT,EACAzD,EACAlC,GAKtC2F,EAAQ9F,wCAA0CqC,EAClDyD,EAAQjG,MAAQM,CAClB,CAvLI4F,CAA+BH,EAAavD,EAAYlC,GACxDkC,EAAWsC,aAAeiB,CAC3B,CACD,OAAOvD,EAAWsC,YACpB,CAEA,SAASjE,GAA2C2B,GAClD,IAAMxB,EAAQwB,EAAWvB,8BAA8BnK,OAEvD,MAAc,YAAVkK,EACK,KAEK,WAAVA,EACK,EAGFwB,EAAW2D,aAAe3D,EAAWpD,eAC9C,CAEgB,SAAAgB,GAAoCoC,EAA0CtC,GAG5F,IAAM4D,EAAkBtB,EAAWH,kBAAkBpM,OAGrD,GAAc,WAFAuM,EAAWvB,8BAA8BnK,QAGrD,GAAqB,IAAjBoJ,EACF,MAAM,IAAIrQ,UAAU,wEAEjB,CAEL,GAAqB,IAAjBqQ,EACF,MAAM,IAAIrQ,UAAU,mFAEtB,GAAIiU,EAAgB9B,YAAc9B,EAAe4D,EAAgBxH,WAC/D,MAAM,IAAIkD,WAAW,4BAExB,CAEDsE,EAAgB5H,OAASH,GAAoB+H,EAAgB5H,QAE7DoJ,GAA4C9C,EAAYtC,EAC1D,CAEgB,SAAAM,GAA+CgC,EACAlC,GAI7D,IAAMwD,EAAkBtB,EAAWH,kBAAkBpM,OAGrD,GAAc,WAFAuM,EAAWvB,8BAA8BnK,QAGrD,GAAwB,IAApBwJ,EAAKhE,WACP,MAAM,IAAIzM,UAAU,yFAItB,GAAwB,IAApByQ,EAAKhE,WACP,MAAM,IAAIzM,UACR,mGAKN,GAAIiU,EAAgB/E,WAAa+E,EAAgB9B,cAAgB1B,EAAKvB,WACpE,MAAM,IAAIS,WAAW,2DAEvB,GAAIsE,EAAgB/B,mBAAqBzB,EAAKpE,OAAOI,WACnD,MAAM,IAAIkD,WAAW,8DAEvB,GAAIsE,EAAgB9B,YAAc1B,EAAKhE,WAAawH,EAAgBxH,WAClE,MAAM,IAAIkD,WAAW,2DAGvB,IAAM4G,EAAiB9F,EAAKhE,WAC5BwH,EAAgB5H,OAASH,GAAoBuE,EAAKpE,QAClDoJ,GAA4C9C,EAAY4D,EAC1D,CAEgB,SAAAC,GAAkC1P,EACA6L,EACA8D,EACAC,EACAC,EACAC,EACA9E,GAOhDa,EAAWvB,8BAAgCtK,EAE3C6L,EAAWQ,YAAa,EACxBR,EAAWO,UAAW,EAEtBP,EAAWsC,aAAe,KAG1BtC,EAAWrD,OAASqD,EAAWpD,qBAAkBxL,EACjD6L,GAAW+C,GAEXA,EAAWzB,iBAAkB,EAC7ByB,EAAWE,UAAW,EAEtBF,EAAW2D,aAAeM,EAE1BjE,EAAWS,eAAiBsD,EAC5B/D,EAAWhB,iBAAmBgF,EAE9BhE,EAAWZ,uBAAyBD,EAEpCa,EAAWH,kBAAoB,IAAIvN,EAEnC6B,EAAOc,0BAA4B+K,EAGnC7O,EACEP,EAFkBkT,MAGlB,WAOE,OANA9D,EAAWE,UAAW,EAKtBJ,GAA6CE,GACtC,IACR,IACD,SAAAlR,GAEE,OADAgQ,GAAkCkB,EAAYlR,GACvC,IACT,GAEJ,CAoDA,SAASyO,GAA+BvN,GACtC,OAAO,IAAI3C,UACT,8CAAuC2C,EAAI,oDAC/C,CAIA,SAASmO,GAAwCnO,GAC/C,OAAO,IAAI3C,UACT,iDAA0C2C,EAAI,uDAClD,CEjnCA,SAASkU,GAAgCC,EAAcnO,GAErD,GAAa,UADbmO,EAAO,GAAAnY,OAAGmY,IAER,MAAM,IAAI9W,UAAU,GAAArB,OAAGgK,EAAY,MAAAhK,OAAAmY,EAAqE,oEAE1G,OAAOA,CACT,CDmBM,SAAUC,GAAgCjQ,GAC9C,OAAO,IAAIkQ,GAAyBlQ,EACtC,CAIgB,SAAAyO,GACdzO,EACA2M,GAKC3M,EAAOE,QAAsC0M,kBAAkBpT,KAAKmT,EACvE,CAiBM,SAAUV,GAAqCjM,GACnD,OAAQA,EAAOE,QAAqC0M,kBAAkBrT,MACxE,CAEM,SAAUyS,GAA4BhM,GAC1C,IAAMD,EAASC,EAAOE,QAEtB,YAAejD,IAAX8C,KAICoQ,GAA2BpQ,EAKlC,CDsRAjE,OAAOkJ,iBAAiB8E,GAA6B1N,UAAW,CAC9D+N,MAAO,CAAElF,YAAY,GACrBuF,QAAS,CAAEvF,YAAY,GACvByF,MAAO,CAAEzF,YAAY,GACrBmK,YAAa,CAAEnK,YAAY,GAC3BiH,YAAa,CAAEjH,YAAY,KAE7BtJ,EAAgBmO,GAA6B1N,UAAU+N,MAAO,SAC9DxO,EAAgBmO,GAA6B1N,UAAUoO,QAAS,WAChE7O,EAAgBmO,GAA6B1N,UAAUsO,MAAO,SAC5B,iBAAvBhT,EAAOyN,aAChBrJ,OAAOC,eAAe+N,GAA6B1N,UAAW1E,EAAOyN,YAAa,CAChF9L,MAAO,+BACP2C,cAAc,IClRlB,IAAAkU,GAAA,WAYE,SAAAA,yBAAYlQ,GAIV,GAHAiC,EAAuBjC,EAAQ,EAAG,4BAClC2C,EAAqB3C,EAAQ,mBAEzByD,GAAuBzD,GACzB,MAAM,IAAI9G,UAAU,+EAGtB,IAAK6Q,GAA+B/J,EAAOc,2BACzC,MAAM,IAAI5H,UAAU,+FAItB4G,EAAsChH,KAAMkH,GAE5ClH,KAAK8T,kBAAoB,IAAIzO,CAC9B,CAoHH,OA9GErC,OAAAC,eAAImU,yBAAM9T,UAAA,SAAA,CAAVsC,IAAA,WACE,OAAKyR,GAA2BrX,MAIzBA,KAAKkI,eAHHtE,EAAoB0T,GAA8B,UAI5D,kCAKDF,yBAAM9T,UAAAuH,OAAN,SAAOhH,GACL,YADK,IAAAA,IAAAA,OAAuBM,GACvBkT,GAA2BrX,WAIEmE,IAA9BnE,KAAKmH,qBACAvD,EAAoBqE,EAAoB,WAG1CN,EAAkC3H,KAAM6D,GAPtCD,EAAoB0T,GAA8B,YAmB7DF,yBAAA9T,UAAAwH,KAAA,SACE+F,EACA0G,GAEA,QAFA,IAAAA,IAAAA,EAAuE,CAAA,IAElEF,GAA2BrX,MAC9B,OAAO4D,EAAoB0T,GAA8B,SAG3D,IAAKrK,YAAY6D,OAAOD,GACtB,OAAOjN,EAAoB,IAAIxD,UAAU,sCAE3C,GAAwB,IAApByQ,EAAKhE,WACP,OAAOjJ,EAAoB,IAAIxD,UAAU,uCAE3C,GAA+B,IAA3ByQ,EAAKpE,OAAOI,WACd,OAAOjJ,EAAoB,IAAIxD,UAAU,gDAE3C,GAAIuM,GAAiBkE,EAAKpE,QACxB,OAAO7I,EAAoB,IAAIxD,UAAU,oCAG3C,IAAIoX,EACJ,IACEA,EC1KU,SACdA,EACAzO,SAIA,OAFAF,EAAiB2O,EAASzO,GAEnB,CACL0L,IAAKhL,EAFqB,QAAhBtG,EAAAqU,aAAA,EAAAA,EAAS/C,WAAO,IAAAtR,EAAAA,EAAA,EAIxB,GAAGpE,OAAAgK,6BAGT,CD8JgB0O,CAAuBF,EAAY,UAC9C,CAAC,MAAO5W,GACP,OAAOiD,EAAoBjD,EAC5B,CACD,IAgBIoK,EACAC,EAjBEyJ,EAAM+C,EAAQ/C,IACpB,GAAY,IAARA,EACF,OAAO7Q,EAAoB,IAAIxD,UAAU,uCAE3C,GF3KE,SAAqByQ,GACzB,OAAOZ,GAAsBY,EAAK2E,YACpC,CEyKSkC,CAAW7G,IAIT,GAAI4D,EAAM5D,EAAKhE,WACpB,OAAOjJ,EAAoB,IAAImM,WAAW,qEAJ1C,GAAI0E,EAAO5D,EAA+BpQ,OACxC,OAAOmD,EAAoB,IAAImM,WAAW,4DAM9C,QAAkC5L,IAA9BnE,KAAKmH,qBACP,OAAOvD,EAAoBqE,EAAoB,cAKjD,IAAMlE,EAAUN,GAA4C,SAAC3B,EAASG,GACpE8I,EAAiBjJ,EACjBkJ,EAAgB/I,CAClB,IAOA,OADA0V,GAA6B3X,KAAM6Q,EAAM4D,EALG,CAC1ClK,YAAa,SAAAF,GAAS,OAAAU,EAAe,CAAExK,MAAO8J,EAAO/J,MAAM,GAAQ,EACnEgK,YAAa,SAAAD,GAAS,OAAAU,EAAe,CAAExK,MAAO8J,EAAO/J,MAAM,GAAO,EAClE4K,YAAa,SAAAvK,GAAK,OAAAqK,EAAcrK,EAAE,IAG7BoD,GAYTqT,yBAAA9T,UAAA6H,YAAA,WACE,IAAKkM,GAA2BrX,MAC9B,MAAMsX,GAA8B,oBAGJnT,IAA9BnE,KAAKmH,sBA8DP,SAA0CF,GAC9CY,EAAmCZ,GACnC,IAAMtG,EAAI,IAAIP,UAAU,uBACxBwX,GAA8C3Q,EAAQtG,EACxD,CA9DIkX,CAAgC7X,OAEnCoX,wBAAD,IAoBM,SAAUC,GAA2B3U,GACzC,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,sBAItCA,aAAa0U,GACtB,CAEM,SAAUO,GACd1Q,EACA4J,EACA4D,EACAZ,GAEA,IAAM3M,EAASD,EAAOE,qBAItBD,EAAOqE,YAAa,EAEE,YAAlBrE,EAAOG,OACTwM,EAAgB3I,YAAYhE,EAAOQ,cAEnC6N,GACErO,EAAOc,0BACP6I,EACA4D,EACAZ,EAGN,CAQgB,SAAA+D,GAA8C3Q,EAAkCtG,GAC9F,IAAMmX,EAAmB7Q,EAAO6M,kBAChC7M,EAAO6M,kBAAoB,IAAIzO,EAC/ByS,EAAiBxR,SAAQ,SAAAuN,GACvBA,EAAgB3I,YAAYvK,EAC9B,GACF,CAIA,SAAS2W,GAA8BvU,GACrC,OAAO,IAAI3C,UACT,6CAAsC2C,EAAI,mDAC9C,CEjUgB,SAAAgV,GAAqBC,EAA2BC,GACtD,IAAAjB,EAAkBgB,EAAQhB,cAElC,QAAsB7S,IAAlB6S,EACF,OAAOiB,EAGT,GAAI9I,GAAY6H,IAAkBA,EAAgB,EAChD,MAAM,IAAIjH,WAAW,yBAGvB,OAAOiH,CACT,CAEM,SAAUkB,GAAwBF,GAC9B,IAAApI,EAASoI,EAAQpI,KAEzB,OAAKA,GACI,WAAM,OAAA,EAIjB,CCtBgB,SAAAuI,GAA0BC,EACArP,GACxCF,EAAiBuP,EAAMrP,GACvB,IAAMiO,EAAgBoB,aAAA,EAAAA,EAAMpB,cACtBpH,EAAOwI,aAAA,EAAAA,EAAMxI,KACnB,MAAO,CACLoH,mBAAiC7S,IAAlB6S,OAA8B7S,EAAYoF,EAA0ByN,GACnFpH,UAAezL,IAATyL,OAAqBzL,EAAYkU,GAA2BzI,EAAM,GAAG7Q,OAAAgK,8BAE/E,CAEA,SAASsP,GAA8BvV,EACAiG,GAErC,OADAC,EAAelG,EAAIiG,GACZ,SAAAsB,GAAS,OAAAd,EAA0BzG,EAAGuH,IAC/C,CCmBA,SAASiO,GACPxV,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAAClF,GAAgB,OAAAuB,EAAYtC,EAAIyV,EAAU,CAAC1U,IACrD,CAEA,SAAS2U,GACP1V,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,WAAM,OAAA3D,EAAYtC,EAAIyV,EAAU,IACzC,CAEA,SAASE,GACP3V,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAACgK,GAAgD,OAAAhO,EAAYjC,EAAIyV,EAAU,CAACxF,IACrF,CAEA,SAAS2F,GACP5V,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAACsB,EAAU0I,GAAgD,OAAA3N,EAAYtC,EAAIyV,EAAU,CAAClO,EAAO0I,GAAY,CAClH,CCrEgB,SAAA4F,GAAqBjW,EAAYqG,GAC/C,IAAK6P,GAAiBlW,GACpB,MAAM,IAAItC,UAAU,UAAG2I,EAAO,6BAElC,CLqPA/F,OAAOkJ,iBAAiBkL,GAAyB9T,UAAW,CAC1DuH,OAAQ,CAAEsB,YAAY,GACtBrB,KAAM,CAAEqB,YAAY,GACpBhB,YAAa,CAAEgB,YAAY,GAC3BC,OAAQ,CAAED,YAAY,KAExBtJ,EAAgBuU,GAAyB9T,UAAUuH,OAAQ,UAC3DhI,EAAgBuU,GAAyB9T,UAAUwH,KAAM,QACzDjI,EAAgBuU,GAAyB9T,UAAU6H,YAAa,eAC9B,iBAAvBvM,EAAOyN,aAChBrJ,OAAOC,eAAemU,GAAyB9T,UAAW1E,EAAOyN,YAAa,CAC5E9L,MAAO,2BACP2C,cAAc,IMtMlB,IAAM2V,GAA8D,mBAA5BC,gBCPxC,IAAAC,GAAA,WAuBE,SAAYA,eAAAC,EACAC,QADA,IAAAD,IAAAA,EAA4D,CAAA,QAC5D,IAAAC,IAAAA,EAAuD,CAAA,QACvC9U,IAAtB6U,EACFA,EAAoB,KAEpB/P,EAAa+P,EAAmB,mBAGlC,IAAMhB,EAAWG,GAAuBc,EAAa,oBAC/CC,EH9EM,SAAyBX,EACAxP,GACvCF,EAAiB0P,EAAUxP,GAC3B,IAAMoQ,EAAQZ,aAAA,EAAAA,EAAUY,MAClB9H,EAAQkH,aAAA,EAAAA,EAAUlH,MAClB+H,EAAQb,aAAA,EAAAA,EAAUa,MAClBC,EAAOd,aAAA,EAAAA,EAAUc,KACjBC,EAAQf,aAAA,EAAAA,EAAUe,MACxB,MAAO,CACLH,WAAiBhV,IAAVgV,OACLhV,EACAmU,GAAmCa,EAAOZ,EAAW,GAAGxZ,OAAAgK,+BAC1DsI,WAAiBlN,IAAVkN,OACLlN,EACAqU,GAAmCnH,EAAOkH,EAAW,GAAGxZ,OAAAgK,+BAC1DqQ,WAAiBjV,IAAViV,OACLjV,EACAsU,GAAmCW,EAAOb,EAAW,GAAGxZ,OAAAgK,+BAC1DuQ,WAAiBnV,IAAVmV,OACLnV,EACAuU,GAAmCY,EAAOf,EAAW,GAAGxZ,OAAAgK,+BAC1DsQ,KAAIA,EAER,CGuD2BE,CAAsBP,EAAmB,mBAKhE,GAHAQ,GAAyBxZ,WAGZmE,IADA+U,EAAeG,KAE1B,MAAM,IAAItJ,WAAW,6BAGvB,IAAM0J,EAAgBvB,GAAqBF,IAq/B/C,SAAmE9Q,EACAgS,EACAlC,EACAyC,GACjE,IAEI5C,EACA6C,EACAC,EACAC,EALE7G,EAAa/P,OAAOuT,OAAOsD,GAAgCvW,WAQ/DuT,OAD2B1S,IAAzB+U,EAAeE,MACA,WAAM,OAAAF,EAAeE,MAAOrG,IAE5B,aAGjB2G,OAD2BvV,IAAzB+U,EAAeI,MACA,SAAAjP,GAAS,OAAA6O,EAAeI,MAAOjP,EAAO0I,IAEtC,WAAM,OAAApP,OAAoBQ,EAApB,EAGvBwV,OAD2BxV,IAAzB+U,EAAe7H,MACA,WAAM,OAAA6H,EAAe7H,OAAf,EAEN,WAAM,OAAA1N,OAAoBQ,EAApB,EAGvByV,OAD2BzV,IAAzB+U,EAAeC,MACA,SAAAtV,GAAU,OAAAqV,EAAeC,MAAOtV,IAEhC,WAAM,OAAAF,OAAoBQ,EAApB,EAGzB2V,GACE5S,EAAQ6L,EAAY8D,EAAgB6C,EAAgBC,EAAgBC,EAAgB5C,EAAeyC,EAEvG,CArhCIM,CAAuD/Z,KAAMkZ,EAFvCnB,GAAqBC,EAAU,GAEuCyB,EAC7F,CAyEH,OApEEzW,OAAAC,eAAI8V,eAAMzV,UAAA,SAAA,CAAVsC,IAAA,WACE,IAAKgT,GAAiB5Y,MACpB,MAAMga,GAA0B,UAGlC,OAAOC,GAAuBja,KAC/B,kCAWD+Y,eAAKzV,UAAA6V,MAAL,SAAMtV,GACJ,YADI,IAAAA,IAAAA,OAAuBM,GACtByU,GAAiB5Y,MAIlBia,GAAuBja,MAClB4D,EAAoB,IAAIxD,UAAU,oDAGpC8Z,GAAoBla,KAAM6D,GAPxBD,EAAoBoW,GAA0B,WAkBzDjB,eAAAzV,UAAA+N,MAAA,WACE,OAAKuH,GAAiB5Y,MAIlBia,GAAuBja,MAClB4D,EAAoB,IAAIxD,UAAU,oDAGvC+Z,GAAoCna,MAC/B4D,EAAoB,IAAIxD,UAAU,2CAGpCga,GAAoBpa,MAXlB4D,EAAoBoW,GAA0B,WAsBzDjB,eAAAzV,UAAA+W,UAAA,WACE,IAAKzB,GAAiB5Y,MACpB,MAAMga,GAA0B,aAGlC,OAAOM,GAAmCta,OAE7C+Y,cAAD,IA0CA,SAASuB,GAAsCpT,GAC7C,OAAO,IAAIqT,GAA4BrT,EACzC,CAqBA,SAASsS,GAA4BtS,GACnCA,EAAOG,OAAS,WAIhBH,EAAOQ,kBAAevD,EAEtB+C,EAAOsT,aAAUrW,EAIjB+C,EAAOuT,+BAA4BtW,EAInC+C,EAAOwT,eAAiB,IAAIrV,EAI5B6B,EAAOyT,2BAAwBxW,EAI/B+C,EAAO0T,mBAAgBzW,EAIvB+C,EAAO2T,2BAAwB1W,EAG/B+C,EAAO4T,0BAAuB3W,EAG9B+C,EAAO6T,eAAgB,CACzB,CAEA,SAASnC,GAAiBlW,GACxB,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,8BAItCA,aAAaqW,GACtB,CAEA,SAASkB,GAAuB/S,GAG9B,YAAuB/C,IAAnB+C,EAAOsT,OAKb,CAEA,SAASN,GAAoBhT,EAAwBrD,SACnD,GAAsB,WAAlBqD,EAAOG,QAAyC,YAAlBH,EAAOG,OACvC,OAAO1D,OAAoBQ,GAE7B+C,EAAOuT,0BAA0BO,aAAenX,UAChDV,EAAA+D,EAAOuT,0BAA0BQ,iCAAkB9B,MAAMtV,GAKzD,IAAM0N,EAAQrK,EAAOG,OAErB,GAAc,WAAVkK,GAAgC,YAAVA,EACxB,OAAO5N,OAAoBQ,GAE7B,QAAoCA,IAAhC+C,EAAO4T,qBACT,OAAO5T,EAAO4T,qBAAqBI,SAKrC,IAAIC,GAAqB,EACX,aAAV5J,IACF4J,GAAqB,EAErBtX,OAASM,GAGX,IAAMJ,EAAUN,GAAsB,SAAC3B,EAASG,GAC9CiF,EAAO4T,qBAAuB,CAC5BI,cAAU/W,EACViX,SAAUtZ,EACVuZ,QAASpZ,EACTqZ,QAASzX,EACT0X,oBAAqBJ,EAEzB,IAOA,OANAjU,EAAO4T,qBAAsBI,SAAWnX,EAEnCoX,GACHK,GAA4BtU,EAAQrD,GAG/BE,CACT,CAEA,SAASqW,GAAoBlT,GAC3B,IAAMqK,EAAQrK,EAAOG,OACrB,GAAc,WAAVkK,GAAgC,YAAVA,EACxB,OAAO3N,EAAoB,IAAIxD,UAC7B,yBAAkBmR,EAAK,+DAM3B,IAkyB+CwB,EAlyBzChP,EAAUN,GAAsB,SAAC3B,EAASG,GAC9C,IAAMwZ,EAA6B,CACjCL,SAAUtZ,EACVuZ,QAASpZ,GAGXiF,EAAO0T,cAAgBa,CACzB,IAEMC,EAASxU,EAAOsT,QAOtB,YANerW,IAAXuX,GAAwBxU,EAAO6T,eAA2B,aAAVxJ,GAClDoK,GAAiCD,GAwxBnC7L,GAD+CkD,EApxBV7L,EAAOuT,0BAqxBXmB,GAAe,GAChDC,GAAoD9I,GApxB7ChP,CACT,CAoBA,SAAS+X,GAAgC5U,EAAwB0K,GAGjD,aAFA1K,EAAOG,OAQrB0U,GAA6B7U,GAL3BsU,GAA4BtU,EAAQ0K,EAMxC,CAEA,SAAS4J,GAA4BtU,EAAwBrD,GAI3D,IAAMkP,EAAa7L,EAAOuT,0BAG1BvT,EAAOG,OAAS,WAChBH,EAAOQ,aAAe7D,EACtB,IAAM6X,EAASxU,EAAOsT,aACPrW,IAAXuX,GACFM,GAAsDN,EAAQ7X,IAsHlE,SAAkDqD,GAChD,QAAqC/C,IAAjC+C,EAAOyT,4BAAwExW,IAAjC+C,EAAO2T,sBACvD,OAAO,EAGT,OAAO,CACT,CAzHOoB,CAAyC/U,IAAW6L,EAAWE,UAClE8I,GAA6B7U,EAEjC,CAEA,SAAS6U,GAA6B7U,GAGpCA,EAAOG,OAAS,UAChBH,EAAOuT,0BAA0B7T,KAEjC,IAAMsV,EAAchV,EAAOQ,aAM3B,GALAR,EAAOwT,eAAepU,SAAQ,SAAA6V,GAC5BA,EAAad,QAAQa,EACvB,IACAhV,EAAOwT,eAAiB,IAAIrV,OAEQlB,IAAhC+C,EAAO4T,qBAAX,CAKA,IAAMsB,EAAelV,EAAO4T,qBAG5B,GAFA5T,EAAO4T,0BAAuB3W,EAE1BiY,EAAab,oBAGf,OAFAa,EAAaf,QAAQa,QACrBG,GAAkDnV,GAKpDhD,EADgBgD,EAAOuT,0BAA0B9T,GAAYyV,EAAad,UAGxE,WAGE,OAFAc,EAAahB,WACbiB,GAAkDnV,GAC3C,IACR,IACD,SAACrD,GAGC,OAFAuY,EAAaf,QAAQxX,GACrBwY,GAAkDnV,GAC3C,IACT,GAvBD,MAFCmV,GAAkDnV,EA0BtD,CA+DA,SAASiT,GAAoCjT,GAC3C,YAA6B/C,IAAzB+C,EAAO0T,oBAAgEzW,IAAjC+C,EAAO2T,qBAKnD,CAuBA,SAASwB,GAAkDnV,QAE5B/C,IAAzB+C,EAAO0T,gBAGT1T,EAAO0T,cAAcS,QAAQnU,EAAOQ,cACpCR,EAAO0T,mBAAgBzW,GAEzB,IAAMuX,EAASxU,EAAOsT,aACPrW,IAAXuX,GACFY,GAAiCZ,EAAQxU,EAAOQ,aAEpD,CAEA,SAAS6U,GAAiCrV,EAAwBsV,GAIhE,IAAMd,EAASxU,EAAOsT,aACPrW,IAAXuX,GAAwBc,IAAiBtV,EAAO6T,gBAC9CyB,EAs0BR,SAAwCd,GAItCe,GAAoCf,EACtC,CA10BMgB,CAA+BhB,GAI/BC,GAAiCD,IAIrCxU,EAAO6T,cAAgByB,CACzB,CAtZAxZ,OAAOkJ,iBAAiB6M,GAAezV,UAAW,CAChD6V,MAAO,CAAEhN,YAAY,GACrBkF,MAAO,CAAElF,YAAY,GACrBkO,UAAW,CAAElO,YAAY,GACzBwQ,OAAQ,CAAExQ,YAAY,KAExBtJ,EAAgBkW,GAAezV,UAAU6V,MAAO,SAChDtW,EAAgBkW,GAAezV,UAAU+N,MAAO,SAChDxO,EAAgBkW,GAAezV,UAAU+W,UAAW,aAClB,iBAAvBzb,EAAOyN,aAChBrJ,OAAOC,eAAe8V,GAAezV,UAAW1E,EAAOyN,YAAa,CAClE9L,MAAO,iBACP2C,cAAc,IAiZlB,IAAAqX,GAAA,WAoBE,SAAAA,4BAAYrT,GAIV,GAHAiC,EAAuBjC,EAAQ,EAAG,+BAClCyR,GAAqBzR,EAAQ,mBAEzB+S,GAAuB/S,GACzB,MAAM,IAAI9G,UAAU,+EAGtBJ,KAAK4c,qBAAuB1V,EAC5BA,EAAOsT,QAAUxa,KAEjB,IAktBoD0b,EAltB9CnK,EAAQrK,EAAOG,OAErB,GAAc,aAAVkK,GACG4I,GAAoCjT,IAAWA,EAAO6T,cACzD0B,GAAoCzc,MAEpC6c,GAA8C7c,MAGhD8c,GAAqC9c,WAChC,GAAc,aAAVuR,EACTwL,GAA8C/c,KAAMkH,EAAOQ,cAC3DoV,GAAqC9c,WAChC,GAAc,WAAVuR,EACTsL,GAA8C7c,MAqsBlD8c,GADsDpB,EAnsBH1b,MAqsBnDgd,GAAkCtB,OApsBzB,CAGL,IAAMQ,EAAchV,EAAOQ,aAC3BqV,GAA8C/c,KAAMkc,GACpDe,GAA+Cjd,KAAMkc,EACtD,CACF,CAqIH,OA/HElZ,OAAAC,eAAIsX,4BAAMjX,UAAA,SAAA,CAAVsC,IAAA,WACE,OAAKsX,GAA8Bld,MAI5BA,KAAKkI,eAHHtE,EAAoBuZ,GAAiC,UAI/D,kCAUDna,OAAAC,eAAIsX,4BAAWjX,UAAA,cAAA,CAAfsC,IAAA,WACE,IAAKsX,GAA8Bld,MACjC,MAAMmd,GAAiC,eAGzC,QAAkChZ,IAA9BnE,KAAK4c,qBACP,MAAMQ,GAA2B,eAGnC,OA+LJ,SAAmD1B,GACjD,IAAMxU,EAASwU,EAAOkB,qBAChBrL,EAAQrK,EAAOG,OAErB,GAAc,YAAVkK,GAAiC,aAAVA,EACzB,OAAO,KAGT,GAAc,WAAVA,EACF,OAAO,EAGT,OAAO8L,GAA8CnW,EAAOuT,0BAC9D,CA5MW6C,CAA0Ctd,KAClD,kCAUDgD,OAAAC,eAAIsX,4BAAKjX,UAAA,QAAA,CAATsC,IAAA,WACE,OAAKsX,GAA8Bld,MAI5BA,KAAKud,cAHH3Z,EAAoBuZ,GAAiC,SAI/D,kCAKD5C,4BAAKjX,UAAA6V,MAAL,SAAMtV,GACJ,YADI,IAAAA,IAAAA,OAAuBM,GACtB+Y,GAA8Bld,WAIDmE,IAA9BnE,KAAK4c,qBACAhZ,EAAoBwZ,GAA2B,UAgH5D,SAA0C1B,EAAqC7X,GAK7E,OAAOqW,GAJQwB,EAAOkB,qBAIa/Y,EACrC,CAnHW2Z,CAAiCxd,KAAM6D,GAPrCD,EAAoBuZ,GAAiC,WAahE5C,4BAAAjX,UAAA+N,MAAA,WACE,IAAK6L,GAA8Bld,MACjC,OAAO4D,EAAoBuZ,GAAiC,UAG9D,IAAMjW,EAASlH,KAAK4c,qBAEpB,YAAezY,IAAX+C,EACKtD,EAAoBwZ,GAA2B,UAGpDjD,GAAoCjT,GAC/BtD,EAAoB,IAAIxD,UAAU,2CAGpCqd,GAAiCzd,OAa1Cua,4BAAAjX,UAAA6H,YAAA,WACE,IAAK+R,GAA8Bld,MACjC,MAAMmd,GAAiC,oBAK1BhZ,IAFAnE,KAAK4c,sBAQpBc,GAAmC1d,OAarCua,4BAAKjX,UAAAgW,MAAL,SAAMjP,GACJ,YADI,IAAAA,IAAAA,OAAWlG,GACV+Y,GAA8Bld,WAIDmE,IAA9BnE,KAAK4c,qBACAhZ,EAAoBwZ,GAA2B,aAGjDO,GAAiC3d,KAAMqK,GAPrCzG,EAAoBuZ,GAAiC,WASjE5C,2BAAD,IAwBA,SAAS2C,GAAuCxa,GAC9C,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,yBAItCA,aAAa6X,GACtB,CAYA,SAASkD,GAAiC/B,GAKxC,OAAOtB,GAJQsB,EAAOkB,qBAKxB,CAqBA,SAASgB,GAAuDlC,EAAqC9J,GAChE,YAA/B8J,EAAOmC,oBACTvB,GAAiCZ,EAAQ9J,GA6f7C,SAAmD8J,EAAqC7X,GAKtFoZ,GAA+CvB,EAAQ7X,EACzD,CAjgBIia,CAA0CpC,EAAQ9J,EAEtD,CAEA,SAASoK,GAAsDN,EAAqC9J,GAChE,YAA9B8J,EAAOqC,mBACTC,GAAgCtC,EAAQ9J,GA8iB5C,SAAkD8J,EAAqC7X,GAIrFkZ,GAA8CrB,EAAQ7X,EACxD,CAjjBIoa,CAAyCvC,EAAQ9J,EAErD,CAiBA,SAAS8L,GAAmChC,GAC1C,IAAMxU,EAASwU,EAAOkB,qBAIhBsB,EAAgB,IAAI9d,UACxB,oFAEF4b,GAAsDN,EAAQwC,GAI9DN,GAAuDlC,EAAQwC,GAE/DhX,EAAOsT,aAAUrW,EACjBuX,EAAOkB,0BAAuBzY,CAChC,CAEA,SAASwZ,GAAoCjC,EAAwCrR,GACnF,IAAMnD,EAASwU,EAAOkB,qBAIhB7J,EAAa7L,EAAOuT,0BAEpB0D,EA+PR,SAAwDpL,EACA1I,GACtD,IACE,OAAO0I,EAAWqL,uBAAuB/T,EAC1C,CAAC,MAAOgU,GAEP,OADAC,GAA6CvL,EAAYsL,GAClD,CACR,CACH,CAvQoBE,CAA4CxL,EAAY1I,GAE1E,GAAInD,IAAWwU,EAAOkB,qBACpB,OAAOhZ,EAAoBwZ,GAA2B,aAGxD,IAAM7L,EAAQrK,EAAOG,OACrB,GAAc,YAAVkK,EACF,OAAO3N,EAAoBsD,EAAOQ,cAEpC,GAAIyS,GAAoCjT,IAAqB,WAAVqK,EACjD,OAAO3N,EAAoB,IAAIxD,UAAU,6DAE3C,GAAc,aAAVmR,EACF,OAAO3N,EAAoBsD,EAAOQ,cAKpC,IAAM3D,EAtiBR,SAAuCmD,GAarC,OATgBzD,GAAsB,SAAC3B,EAASG,GAC9C,IAAMka,EAA6B,CACjCf,SAAUtZ,EACVuZ,QAASpZ,GAGXiF,EAAOwT,eAAeha,KAAKyb,EAC7B,GAGF,CAwhBkBqC,CAA8BtX,GAI9C,OAsPF,SAAiD6L,EACA1I,EACA8T,GAC/C,IACEtO,GAAqBkD,EAAY1I,EAAO8T,EACzC,CAAC,MAAOM,GAEP,YADAH,GAA6CvL,EAAY0L,EAE1D,CAED,IAAMvX,EAAS6L,EAAW2L,0BAC1B,IAAKvE,GAAoCjT,IAA6B,aAAlBA,EAAOG,OAAuB,CAEhFkV,GAAiCrV,EADZyX,GAA+C5L,GAErE,CAED8I,GAAoD9I,EACtD,CAzQE6L,CAAqC7L,EAAY1I,EAAO8T,GAEjDpa,CACT,CAvJAf,OAAOkJ,iBAAiBqO,GAA4BjX,UAAW,CAC7D6V,MAAO,CAAEhN,YAAY,GACrBkF,MAAO,CAAElF,YAAY,GACrBhB,YAAa,CAAEgB,YAAY,GAC3BmN,MAAO,CAAEnN,YAAY,GACrBC,OAAQ,CAAED,YAAY,GACtBiH,YAAa,CAAEjH,YAAY,GAC3ByI,MAAO,CAAEzI,YAAY,KAEvBtJ,EAAgB0X,GAA4BjX,UAAU6V,MAAO,SAC7DtW,EAAgB0X,GAA4BjX,UAAU+N,MAAO,SAC7DxO,EAAgB0X,GAA4BjX,UAAU6H,YAAa,eACnEtI,EAAgB0X,GAA4BjX,UAAUgW,MAAO,SAC3B,iBAAvB1a,EAAOyN,aAChBrJ,OAAOC,eAAesX,GAA4BjX,UAAW1E,EAAOyN,YAAa,CAC/E9L,MAAO,8BACP2C,cAAc,IAyIlB,IAAM0Y,GAA+B,CAAA,EASrC/B,GAAA,WAwBE,SAAAA,kCACE,MAAM,IAAIzZ,UAAU,sBACrB,CAgEH,OAvDE4C,OAAAC,eAAI4W,gCAAWvW,UAAA,cAAA,CAAfsC,IAAA,WACE,IAAKiZ,GAAkC7e,MACrC,MAAM8e,GAAqC,eAE7C,OAAO9e,KAAKgb,YACb,kCAKDhY,OAAAC,eAAI4W,gCAAMvW,UAAA,SAAA,CAAVsC,IAAA,WACE,IAAKiZ,GAAkC7e,MACrC,MAAM8e,GAAqC,UAE7C,QAA8B3a,IAA1BnE,KAAKib,iBAIP,MAAM,IAAI7a,UAAU,qEAEtB,OAAOJ,KAAKib,iBAAiB8D,MAC9B,kCASDlF,gCAAKvW,UAAAsO,MAAL,SAAMjR,GACJ,QADI,IAAAA,IAAAA,OAAkBwD,IACjB0a,GAAkC7e,MACrC,MAAM8e,GAAqC,SAG/B,aADA9e,KAAK0e,0BAA0BrX,QAO7C2X,GAAqChf,KAAMW,IAI7CkZ,gCAAAvW,UAACqD,GAAD,SAAa9C,GACX,IAAMgL,EAAS7O,KAAKif,gBAAgBpb,GAEpC,OADAqb,GAA+Clf,MACxC6O,GAITgL,gCAACvW,UAAAsD,GAAD,WACEoJ,GAAWhQ,OAEd6Z,+BAAD,IAgBA,SAASgF,GAAkCnc,GACzC,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,8BAItCA,aAAamX,GACtB,CAEA,SAASC,GAAwC5S,EACA6L,EACA8D,EACA6C,EACAC,EACAC,EACA5C,EACAyC,GAI/C1G,EAAW2L,0BAA4BxX,EACvCA,EAAOuT,0BAA4B1H,EAGnCA,EAAWrD,YAASvL,EACpB4O,EAAWpD,qBAAkBxL,EAC7B6L,GAAW+C,GAEXA,EAAWiI,kBAAe7W,EAC1B4O,EAAWkI,4BD/+BX,GAAIpC,GACF,OAAO,IAAKC,eAGhB,CC2+BgCqG,GAC9BpM,EAAWE,UAAW,EAEtBF,EAAWqL,uBAAyB3E,EACpC1G,EAAW2D,aAAeM,EAE1BjE,EAAWqM,gBAAkB1F,EAC7B3G,EAAWsM,gBAAkB1F,EAC7B5G,EAAWkM,gBAAkBrF,EAE7B,IAAM4C,EAAemC,GAA+C5L,GACpEwJ,GAAiCrV,EAAQsV,GAIzCtY,EADqBP,EADDkT,MAIlB,WAIE,OAFA9D,EAAWE,UAAW,EACtB4I,GAAoD9I,GAC7C,IACR,IACD,SAAAlR,GAIE,OAFAkR,EAAWE,UAAW,EACtB6I,GAAgC5U,EAAQrF,GACjC,IACT,GAEJ,CAwCA,SAASqd,GAA+CnM,GACtDA,EAAWqM,qBAAkBjb,EAC7B4O,EAAWsM,qBAAkBlb,EAC7B4O,EAAWkM,qBAAkB9a,EAC7B4O,EAAWqL,4BAAyBja,CACtC,CAiBA,SAASkZ,GAA8CtK,GACrD,OAAOA,EAAW2D,aAAe3D,EAAWpD,eAC9C,CAuBA,SAASkM,GAAuD9I,GAC9D,IAAM7L,EAAS6L,EAAW2L,0BAE1B,GAAK3L,EAAWE,eAIqB9O,IAAjC+C,EAAOyT,sBAMX,GAAc,aAFAzT,EAAOG,QAOrB,GAAiC,IAA7B0L,EAAWrD,OAAOjP,OAAtB,CAIA,IAAMF,EAAuBwS,EVzpCNrD,OAAOlJ,OAClBjG,MUypCRA,IAAUqb,GAahB,SAAqD7I,GACnD,IAAM7L,EAAS6L,EAAW2L,2BArrB5B,SAAgDxX,GAG9CA,EAAO2T,sBAAwB3T,EAAO0T,cACtC1T,EAAO0T,mBAAgBzW,CACzB,EAkrBEmb,CAAuCpY,GAEvCqI,GAAawD,GAGb,IAAMwM,EAAmBxM,EAAWsM,kBACpCH,GAA+CnM,GAC/C7O,EACEqb,GACA,WAEE,OA/vBN,SAA2CrY,GAEzCA,EAAO2T,sBAAuBO,cAASjX,GACvC+C,EAAO2T,2BAAwB1W,EAMjB,aAJA+C,EAAOG,SAMnBH,EAAOQ,kBAAevD,OACcA,IAAhC+C,EAAO4T,uBACT5T,EAAO4T,qBAAqBM,WAC5BlU,EAAO4T,0BAAuB3W,IAIlC+C,EAAOG,OAAS,SAEhB,IAAMqU,EAASxU,EAAOsT,aACPrW,IAAXuX,GACFsB,GAAkCtB,EAKtC,CAmuBM8D,CAAkCtY,GAC3B,IACR,IACD,SAAArD,GAEE,OAtuBN,SAAoDqD,EAAwB0K,GAE1E1K,EAAO2T,sBAAuBQ,QAAQzJ,GACtC1K,EAAO2T,2BAAwB1W,OAKKA,IAAhC+C,EAAO4T,uBACT5T,EAAO4T,qBAAqBO,QAAQzJ,GACpC1K,EAAO4T,0BAAuB3W,GAEhC2X,GAAgC5U,EAAQ0K,EAC1C,CAwtBM6N,CAA2CvY,EAAQrD,GAC5C,IACT,GAEJ,CAjCI6b,CAA4C3M,GAmChD,SAAwDA,EAAgD1I,GACtG,IAAMnD,EAAS6L,EAAW2L,2BArsB5B,SAAqDxX,GAGnDA,EAAOyT,sBAAwBzT,EAAOwT,eAAevY,OACvD,CAmsBEwd,CAA4CzY,GAE5C,IAAM0Y,EAAmB7M,EAAWqM,gBAAgB/U,GACpDnG,EACE0b,GACA,YAhyBJ,SAA2C1Y,GAEzCA,EAAOyT,sBAAuBS,cAASjX,GACvC+C,EAAOyT,2BAAwBxW,CACjC,CA6xBM0b,CAAkC3Y,GAElC,IAAMqK,EAAQrK,EAAOG,OAKrB,GAFAkI,GAAawD,IAERoH,GAAoCjT,IAAqB,aAAVqK,EAAsB,CACxE,IAAMiL,EAAemC,GAA+C5L,GACpEwJ,GAAiCrV,EAAQsV,EAC1C,CAGD,OADAX,GAAoD9I,GAC7C,IACR,IACD,SAAAlP,GAKE,MAJsB,aAAlBqD,EAAOG,QACT6X,GAA+CnM,GA5yBvD,SAAoD7L,EAAwB0K,GAE1E1K,EAAOyT,sBAAuBU,QAAQzJ,GACtC1K,EAAOyT,2BAAwBxW,EAI/B2X,GAAgC5U,EAAQ0K,EAC1C,CAsyBMkO,CAA2C5Y,EAAQrD,GAC5C,IACT,GAEJ,CAjEIkc,CAA4ChN,EAAYxS,EANzD,OANCwb,GAA6B7U,EAcjC,CAEA,SAASoX,GAA6CvL,EAAkDnB,GAClD,aAAhDmB,EAAW2L,0BAA0BrX,QACvC2X,GAAqCjM,EAAYnB,EAErD,CA2DA,SAAS+M,GAA+C5L,GAEtD,OADoBsK,GAA8CtK,IAC5C,CACxB,CAIA,SAASiM,GAAqCjM,EAAkDnB,GAC9F,IAAM1K,EAAS6L,EAAW2L,0BAI1BQ,GAA+CnM,GAC/CyI,GAA4BtU,EAAQ0K,EACtC,CAIA,SAASoI,GAA0BjX,GACjC,OAAO,IAAI3C,UAAU,mCAA4B2C,EAAI,yCACvD,CAIA,SAAS+b,GAAqC/b,GAC5C,OAAO,IAAI3C,UACT,oDAA6C2C,EAAI,0DACrD,CAKA,SAASoa,GAAiCpa,GACxC,OAAO,IAAI3C,UACT,gDAAyC2C,EAAI,sDACjD,CAEA,SAASqa,GAA2Bra,GAClC,OAAO,IAAI3C,UAAU,UAAY2C,EAAO,oCAC1C,CAEA,SAAS+Z,GAAqCpB,GAC5CA,EAAOxT,eAAiBzE,GAAW,SAAC3B,EAASG,GAC3CyZ,EAAOvT,uBAAyBrG,EAChC4Z,EAAOtT,sBAAwBnG,EAC/ByZ,EAAOmC,oBAAsB,SAC/B,GACF,CAEA,SAASZ,GAA+CvB,EAAqC7X,GAC3FiZ,GAAqCpB,GACrCY,GAAiCZ,EAAQ7X,EAC3C,CAOA,SAASyY,GAAiCZ,EAAqC7X,QACxCM,IAAjCuX,EAAOtT,wBAKX3D,EAA0BiX,EAAOxT,gBACjCwT,EAAOtT,sBAAsBvE,GAC7B6X,EAAOvT,4BAAyBhE,EAChCuX,EAAOtT,2BAAwBjE,EAC/BuX,EAAOmC,oBAAsB,WAC/B,CAUA,SAASb,GAAkCtB,QACHvX,IAAlCuX,EAAOvT,yBAKXuT,EAAOvT,4BAAuBhE,GAC9BuX,EAAOvT,4BAAyBhE,EAChCuX,EAAOtT,2BAAwBjE,EAC/BuX,EAAOmC,oBAAsB,WAC/B,CAEA,SAASpB,GAAoCf,GAC3CA,EAAO6B,cAAgB9Z,GAAW,SAAC3B,EAASG,GAC1CyZ,EAAOsE,sBAAwBle,EAC/B4Z,EAAOuE,qBAAuBhe,CAChC,IACAyZ,EAAOqC,mBAAqB,SAC9B,CAEA,SAAShB,GAA8CrB,EAAqC7X,GAC1F4Y,GAAoCf,GACpCsC,GAAgCtC,EAAQ7X,EAC1C,CAEA,SAASgZ,GAA8CnB,GACrDe,GAAoCf,GACpCC,GAAiCD,EACnC,CAEA,SAASsC,GAAgCtC,EAAqC7X,QACxCM,IAAhCuX,EAAOuE,uBAIXxb,EAA0BiX,EAAO6B,eACjC7B,EAAOuE,qBAAqBpc,GAC5B6X,EAAOsE,2BAAwB7b,EAC/BuX,EAAOuE,0BAAuB9b,EAC9BuX,EAAOqC,mBAAqB,WAC9B,CAgBA,SAASpC,GAAiCD,QACHvX,IAAjCuX,EAAOsE,wBAIXtE,EAAOsE,2BAAsB7b,GAC7BuX,EAAOsE,2BAAwB7b,EAC/BuX,EAAOuE,0BAAuB9b,EAC9BuX,EAAOqC,mBAAqB,YAC9B,CAjZA/a,OAAOkJ,iBAAiB2N,GAAgCvW,UAAW,CACjE4c,YAAa,CAAE/T,YAAY,GAC3B4S,OAAQ,CAAE5S,YAAY,GACtByF,MAAO,CAAEzF,YAAY,KAEW,iBAAvBvN,EAAOyN,aAChBrJ,OAAOC,eAAe4W,GAAgCvW,UAAW1E,EAAOyN,YAAa,CACnF9L,MAAO,kCACP2C,cAAc,ICrgCX,IAAMid,GAVe,oBAAfC,WACFA,WACkB,oBAATC,KACTA,KACoB,oBAAXC,OACTA,YADF,ECiDT,IAxBQpQ,GAwBFqQ,IA7CN,SAAmCrQ,GACjC,GAAsB,mBAATA,GAAuC,iBAATA,EACzC,OAAO,EAET,GAA+C,iBAA1CA,EAAiCnN,KACpC,OAAO,EAET,IAEE,OADA,IAAKmN,GACE,CACR,CAAC,MAAA/M,GACA,OAAO,CACR,CACH,CASSqd,CADDtQ,GAAOiQ,cAAA,EAAAA,GAASI,cACmBrQ,QAAO/L,IAOlD,WAEE,IAAM+L,EAAO,SAA0CuQ,EAAkB1d,GACvE/C,KAAKygB,QAAUA,GAAW,GAC1BzgB,KAAK+C,KAAOA,GAAQ,QAChB2d,MAAMC,mBACRD,MAAMC,kBAAkB3gB,KAAMA,KAAKwV,YAEvC,EAIA,OAHA3S,EAAgBqN,EAAM,gBACtBA,EAAK5M,UAAYN,OAAOuT,OAAOmK,MAAMpd,WACrCN,OAAOC,eAAeiN,EAAK5M,UAAW,cAAe,CAAE/C,MAAO2P,EAAM0Q,UAAU,EAAM1d,cAAc,IAC3FgN,CACT,CAGiE2Q,GC5BjD,SAAAC,GAAwBC,EACAnV,EACAoV,EACAC,EACA7S,EACA2Q,GAUtC,IAAM9X,EAAS8C,EAAsCgX,GAC/CrF,EAASpB,GAAsC1O,GAErDmV,EAAOxV,YAAa,EAEpB,IAAI2V,GAAe,EAGfC,EAAexd,OAA0BQ,GAE7C,OAAOV,GAAW,SAAC3B,EAASG,GAC1B,IAAI2X,EAwIuB1S,EAAyCnD,EAAwBqd,EAvI5F,QAAejd,IAAX4a,EAAsB,CAuBxB,GAtBAnF,EAAiB,WACf,IAAMhI,OAA0BzN,IAAlB4a,EAAOlb,OAAuBkb,EAAOlb,OAAS,IAAI0c,GAAa,UAAW,cAClFc,EAAsC,GACvCJ,GACHI,EAAQ3gB,MAAK,WACX,MAAoB,aAAhBkL,EAAKvE,OACA6S,GAAoBtO,EAAMgG,GAE5BjO,OAAoBQ,EAC7B,IAEGiK,GACHiT,EAAQ3gB,MAAK,WACX,MAAsB,aAAlBqgB,EAAO1Z,OACFO,GAAqBmZ,EAAQnP,GAE/BjO,OAAoBQ,EAC7B,IAEFmd,GAAmB,WAAM,OAAA7f,QAAQ8f,IAAIF,EAAQG,KAAI,SAAAJ,GAAU,OAAAA,OAAU,IAAE,EAAMxP,EAC/E,EAEImN,EAAO0C,QAET,YADA7H,IAIFmF,EAAO2C,iBAAiB,QAAS9H,EAClC,CA0ED,GA9BA+H,EAAmBZ,EAAQ9Z,EAAOiB,gBAAgB,SAAAgU,GAMhD,OALK+E,EAGHW,GAAS,EAAM1F,GAFfoF,GAAmB,WAAM,OAAApH,GAAoBtO,EAAMsQ,EAAY,IAAE,EAAMA,GAIlE,IACT,IAGAyF,EAAmB/V,EAAM8P,EAAOxT,gBAAgB,SAAAgU,GAM9C,OALK9N,EAGHwT,GAAS,EAAM1F,GAFfoF,GAAmB,WAAM,OAAA1Z,GAAqBmZ,EAAQ7E,EAAY,IAAE,EAAMA,GAIrE,IACT,IA6C2BhV,EA1CT6Z,EA0CkDhd,EA1C1CkD,EAAOiB,eA0C2DkZ,EA1C3C,WAM/C,OALKJ,EAGHY,IAFAN,GAAmB,WAAM,OH0qBjC,SAA8D5F,GAC5D,IAAMxU,EAASwU,EAAOkB,qBAIhBrL,EAAQrK,EAAOG,OACrB,OAAI8S,GAAoCjT,IAAqB,WAAVqK,EAC1C5N,OAAoBQ,GAGf,YAAVoN,EACK3N,EAAoBsD,EAAOQ,cAK7B+V,GAAiC/B,EAC1C,CG3rBiCmG,CAAqDnG,EAAO,IAIhF,IACT,EAoCwB,WAAlBxU,EAAOG,OACT+Z,IAEAhd,EAAgBL,EAASqd,GApCzBjH,GAAoCvO,IAAyB,WAAhBA,EAAKvE,OAAqB,CACzE,IAAMya,EAAa,IAAI1hB,UAAU,+EAE5BgO,EAGHwT,GAAS,EAAME,GAFfR,GAAmB,WAAM,OAAA1Z,GAAqBmZ,EAAQe,EAAW,IAAE,EAAMA,EAI5E,CAID,SAASC,IAGP,IAAMC,EAAkBb,EACxB,OAAOrd,EACLqd,GACA,WAAM,OAAAa,IAAoBb,EAAeY,SAA0B5d,CAAS,GAE/E,CAED,SAASwd,EAAmBza,EACAnD,EACAqd,GACJ,YAAlBla,EAAOG,OACT+Z,EAAOla,EAAOQ,cAEdrD,EAAcN,EAASqd,EAE1B,CAUD,SAASE,EAAmBF,EAAgCa,EAA2BC,GAYrF,SAASC,IAMP,OALAje,EACEkd,KACA,WAAM,OAAAgB,EAASH,EAAiBC,EAAc,IAC9C,SAAAG,GAAY,OAAAD,GAAS,EAAMC,EAAS,IAE/B,IACR,CAlBGnB,IAGJA,GAAe,EAEK,aAAhBtV,EAAKvE,QAA0B8S,GAAoCvO,GAGrEuW,IAFA/d,EAAgB2d,IAAyBI,GAa5C,CAED,SAASP,EAASU,EAAmB1Q,GAC/BsP,IAGJA,GAAe,EAEK,aAAhBtV,EAAKvE,QAA0B8S,GAAoCvO,GAGrEwW,EAASE,EAAS1Q,GAFlBxN,EAAgB2d,KAAyB,WAAM,OAAAK,EAASE,EAAS1Q,EAAlB,IAIlD,CAED,SAASwQ,EAASE,EAAmB1Q,GAanC,OAZA8L,GAAmChC,GACnC7T,EAAmCZ,QAEpB9C,IAAX4a,GACFA,EAAOwD,oBAAoB,QAAS3I,GAElC0I,EACFrgB,EAAO2P,GAEP9P,OAAQqC,GAGH,IACR,CA/EDM,EA9EShB,GAAiB,SAAC+e,EAAaC,IACpC,SAAS7iB,EAAKU,GACRA,EACFkiB,IAIA1e,EASFod,EACKvd,GAAoB,GAGtBG,EAAmB4X,EAAO6B,eAAe,WAC9C,OAAO9Z,GAAoB,SAACif,EAAaC,GACvC1X,GACEhE,EACA,CACEsD,YAAa,SAAAF,GACX8W,EAAerd,EAAmB6Z,GAAiCjC,EAAQrR,QAAQlG,EAAW3B,GAC9FkgB,GAAY,EACb,EACDpY,YAAa,WAAM,OAAAoY,GAAY,EAAK,EACpCxX,YAAayX,GAGnB,GACF,IA3BqC/iB,EAAM6iB,EAExC,CAED7iB,EAAK,EACP,IAkJJ,GACF,CCpOA,IAAAgjB,GAAA,WAwBE,SAAAA,kCACE,MAAM,IAAIxiB,UAAU,sBACrB,CA0FH,OApFE4C,OAAAC,eAAI2f,gCAAWtf,UAAA,cAAA,CAAfsC,IAAA,WACE,IAAKid,GAAkC7iB,MACrC,MAAM8e,GAAqC,eAG7C,OAAOgE,GAA8C9iB,KACtD,kCAMD4iB,gCAAAtf,UAAA+N,MAAA,WACE,IAAKwR,GAAkC7iB,MACrC,MAAM8e,GAAqC,SAG7C,IAAKiE,GAAiD/iB,MACpD,MAAM,IAAII,UAAU,mDAGtB4iB,GAAqChjB,OAOvC4iB,gCAAOtf,UAAAoO,QAAP,SAAQrH,GACN,QADM,IAAAA,IAAAA,OAAWlG,IACZ0e,GAAkC7iB,MACrC,MAAM8e,GAAqC,WAG7C,IAAKiE,GAAiD/iB,MACpD,MAAM,IAAII,UAAU,qDAGtB,OAAO6iB,GAAuCjjB,KAAMqK,IAMtDuY,gCAAKtf,UAAAsO,MAAL,SAAMjR,GACJ,QADI,IAAAA,IAAAA,OAAkBwD,IACjB0e,GAAkC7iB,MACrC,MAAM8e,GAAqC,SAG7CoE,GAAqCljB,KAAMW,IAI7CiiB,gCAAAtf,UAACuD,GAAD,SAAchD,GACZmM,GAAWhQ,MACX,IAAM6O,EAAS7O,KAAK+R,iBAAiBlO,GAErC,OADAsf,GAA+CnjB,MACxC6O,GAIT+T,gCAAAtf,UAACwD,GAAD,SAAYoD,GACV,IAAMhD,EAASlH,KAAKojB,0BAEpB,GAAIpjB,KAAK0P,OAAOjP,OAAS,EAAG,CAC1B,IAAM4J,EAAQkF,GAAavP,MAEvBA,KAAKsR,iBAA0C,IAAvBtR,KAAK0P,OAAOjP,QACtC0iB,GAA+CnjB,MAC/CoV,GAAoBlO,IAEpBmc,GAAgDrjB,MAGlDkK,EAAYK,YAAYF,EACzB,MACCJ,EAA6B/C,EAAQgD,GACrCmZ,GAAgDrjB,OAKpD4iB,gCAACtf,UAAAyD,GAAD,aAGD6b,+BAAD,IAoBA,SAASC,GAA2CngB,GAClD,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,8BAItCA,aAAakgB,GACtB,CAEA,SAASS,GAAgDtQ,GACpCuQ,GAA8CvQ,KAK7DA,EAAWO,SACbP,EAAWQ,YAAa,GAM1BR,EAAWO,UAAW,EAGtBpP,EADoB6O,EAAWS,kBAG7B,WAQE,OAPAT,EAAWO,UAAW,EAElBP,EAAWQ,aACbR,EAAWQ,YAAa,EACxB8P,GAAgDtQ,IAG3C,IACR,IACD,SAAApS,GAEE,OADAuiB,GAAqCnQ,EAAYpS,GAC1C,IACT,KAEJ,CAEA,SAAS2iB,GAA8CvQ,GACrD,IAAM7L,EAAS6L,EAAWqQ,0BAE1B,QAAKL,GAAiDhQ,OAIjDA,EAAWE,cAIZtI,GAAuBzD,IAAWsD,GAAiCtD,GAAU,IAI7D4b,GAA8C/P,GAE/C,GAKrB,CAEA,SAASoQ,GAA+CpQ,GACtDA,EAAWS,oBAAiBrP,EAC5B4O,EAAWhB,sBAAmB5N,EAC9B4O,EAAWqL,4BAAyBja,CACtC,CAIM,SAAU6e,GAAqCjQ,GACnD,GAAKgQ,GAAiDhQ,GAAtD,CAIA,IAAM7L,EAAS6L,EAAWqQ,0BAE1BrQ,EAAWzB,iBAAkB,EAEI,IAA7ByB,EAAWrD,OAAOjP,SACpB0iB,GAA+CpQ,GAC/CqC,GAAoBlO,GARrB,CAUH,CAEgB,SAAA+b,GACdlQ,EACA1I,GAEA,GAAK0Y,GAAiDhQ,GAAtD,CAIA,IAAM7L,EAAS6L,EAAWqQ,0BAE1B,GAAIzY,GAAuBzD,IAAWsD,GAAiCtD,GAAU,EAC/EkD,GAAiClD,EAAQmD,GAAO,OAC3C,CACL,IAAI8T,SACJ,IACEA,EAAYpL,EAAWqL,uBAAuB/T,EAC/C,CAAC,MAAOgU,GAEP,MADA6E,GAAqCnQ,EAAYsL,GAC3CA,CACP,CAED,IACExO,GAAqBkD,EAAY1I,EAAO8T,EACzC,CAAC,MAAOM,GAEP,MADAyE,GAAqCnQ,EAAY0L,GAC3CA,CACP,CACF,CAED4E,GAAgDtQ,EAvB/C,CAwBH,CAEgB,SAAAmQ,GAAqCnQ,EAAkDpS,GACrG,IAAMuG,EAAS6L,EAAWqQ,0BAEJ,aAAlBlc,EAAOG,SAIX2I,GAAW+C,GAEXoQ,GAA+CpQ,GAC/CqD,GAAoBlP,EAAQvG,GAC9B,CAEM,SAAUmiB,GACd/P,GAEA,IAAMxB,EAAQwB,EAAWqQ,0BAA0B/b,OAEnD,MAAc,YAAVkK,EACK,KAEK,WAAVA,EACK,EAGFwB,EAAW2D,aAAe3D,EAAWpD,eAC9C,CAaM,SAAUoT,GACdhQ,GAEA,IAAMxB,EAAQwB,EAAWqQ,0BAA0B/b,OAEnD,OAAK0L,EAAWzB,iBAA6B,aAAVC,CAKrC,CAEgB,SAAAgS,GAAwCrc,EACA6L,EACA8D,EACAC,EACAC,EACAC,EACAyC,GAGtD1G,EAAWqQ,0BAA4Blc,EAEvC6L,EAAWrD,YAASvL,EACpB4O,EAAWpD,qBAAkBxL,EAC7B6L,GAAW+C,GAEXA,EAAWE,UAAW,EACtBF,EAAWzB,iBAAkB,EAC7ByB,EAAWQ,YAAa,EACxBR,EAAWO,UAAW,EAEtBP,EAAWqL,uBAAyB3E,EACpC1G,EAAW2D,aAAeM,EAE1BjE,EAAWS,eAAiBsD,EAC5B/D,EAAWhB,iBAAmBgF,EAE9B7P,EAAOc,0BAA4B+K,EAGnC7O,EACEP,EAFkBkT,MAGlB,WAOE,OANA9D,EAAWE,UAAW,EAKtBoQ,GAAgDtQ,GACzC,IACR,IACD,SAAAlR,GAEE,OADAqhB,GAAqCnQ,EAAYlR,GAC1C,IACT,GAEJ,CAqCA,SAASid,GAAqC/b,GAC5C,OAAO,IAAI3C,UACT,oDAA6C2C,EAAI,0DACrD,CCxXgB,SAAAygB,GAAqBtc,EACAuc,GAGnC,OAAIxS,GAA+B/J,EAAOc,2BAkItC,SAAgCd,GAIpC,IAMIwc,EACAC,EACAC,EACAC,EAEAC,EAXA7c,EAAsD8C,EAAmC7C,GACzF6c,GAAU,EACVC,GAAsB,EACtBC,GAAsB,EACtBC,GAAY,EACZC,GAAY,EAOVC,EAAgB3gB,GAAiB,SAAA3B,GACrCgiB,EAAuBhiB,CACzB,IAEA,SAASuiB,EAAmBC,GAC1BjgB,EAAcigB,EAAWpc,gBAAgB,SAAArG,GACvC,OAAIyiB,IAAerd,IAGnB4K,GAAkC+R,EAAQ5b,0BAA2BnG,GACrEgQ,GAAkCgS,EAAQ7b,0BAA2BnG,GAChEqiB,GAAcC,GACjBL,OAAqB3f,IALd,IAQX,GACD,CAED,SAASogB,IACHlN,GAA2BpQ,KAE7BY,EAAmCZ,GAGnCod,EADApd,EAAS8C,EAAmC7C,KA+D9C+D,GAAgChE,EA3DwB,CACtDsD,YAAa,SAAAF,GAIXzF,GAAe,WACbof,GAAsB,EACtBC,GAAsB,EAEtB,IAAMO,EAASna,EACXoa,EAASpa,EACb,IAAK6Z,IAAcC,EACjB,IACEM,EAASpV,GAAkBhF,EAC5B,CAAC,MAAO8J,GAIP,OAHAtC,GAAkC+R,EAAQ5b,0BAA2BmM,GACrEtC,GAAkCgS,EAAQ7b,0BAA2BmM,QACrE2P,EAAqBlc,GAAqBV,EAAQiN,GAEnD,CAGE+P,GACHvS,GAAoCiS,EAAQ5b,0BAA2Bwc,GAEpEL,GACHxS,GAAoCkS,EAAQ7b,0BAA2Byc,GAGzEV,GAAU,EACNC,EACFU,IACST,GACTU,GAEJ,GACD,EACDra,YAAa,WACXyZ,GAAU,EACLG,GACHzS,GAAkCmS,EAAQ5b,2BAEvCmc,GACH1S,GAAkCoS,EAAQ7b,2BAExC4b,EAAQ5b,0BAA0B4K,kBAAkBnS,OAAS,GAC/DkQ,GAAoCiT,EAAQ5b,0BAA2B,GAErE6b,EAAQ7b,0BAA0B4K,kBAAkBnS,OAAS,GAC/DkQ,GAAoCkT,EAAQ7b,0BAA2B,GAEpEkc,GAAcC,GACjBL,OAAqB3f,EAExB,EACD+G,YAAa,WACX6Y,GAAU,CACX,GAGJ,CAED,SAASa,EAAmB/T,EAAkCgU,GACxDna,GAAqDzD,KAEvDY,EAAmCZ,GAGnCod,EADApd,EAASkQ,GAAgCjQ,KAI3C,IAAM4d,EAAaD,EAAahB,EAAUD,EACpCmB,EAAcF,EAAajB,EAAUC,EAwE3ClM,GAA6B1Q,EAAQ4J,EAAM,EAtE0B,CACnEtG,YAAa,SAAAF,GAIXzF,GAAe,WACbof,GAAsB,EACtBC,GAAsB,EAEtB,IAAMe,EAAeH,EAAaV,EAAYD,EAG9C,GAFsBW,EAAaX,EAAYC,EAgBnCa,GACVjU,GAA+C+T,EAAW9c,0BAA2BqC,OAfnE,CAClB,IAAI6J,SACJ,IACEA,EAAc7E,GAAkBhF,EACjC,CAAC,MAAO8J,GAIP,OAHAtC,GAAkCiT,EAAW9c,0BAA2BmM,GACxEtC,GAAkCkT,EAAY/c,0BAA2BmM,QACzE2P,EAAqBlc,GAAqBV,EAAQiN,GAEnD,CACI6Q,GACHjU,GAA+C+T,EAAW9c,0BAA2BqC,GAEvFsH,GAAoCoT,EAAY/c,0BAA2BkM,EAC5E,CAID6P,GAAU,EACNC,EACFU,IACST,GACTU,GAEJ,GACD,EACDra,YAAa,SAAAD,GACX0Z,GAAU,EAEV,IAAMiB,EAAeH,EAAaV,EAAYD,EACxCe,EAAgBJ,EAAaX,EAAYC,EAE1Ca,GACHvT,GAAkCqT,EAAW9c,2BAE1Cid,GACHxT,GAAkCsT,EAAY/c,gCAGlC7D,IAAVkG,IAGG2a,GACHjU,GAA+C+T,EAAW9c,0BAA2BqC,IAElF4a,GAAiBF,EAAY/c,0BAA0B4K,kBAAkBnS,OAAS,GACrFkQ,GAAoCoU,EAAY/c,0BAA2B,IAI1Egd,GAAiBC,GACpBnB,OAAqB3f,EAExB,EACD+G,YAAa,WACX6Y,GAAU,CACX,GAGJ,CAED,SAASW,IACP,GAAIX,EAEF,OADAC,GAAsB,EACfrgB,OAAoBQ,GAG7B4f,GAAU,EAEV,IAAMzN,EAAcnF,GAA2CyS,EAAQ5b,2BAOvE,OANoB,OAAhBsO,EACFiO,IAEAK,EAAmBtO,EAAY/F,OAAQ,GAGlC5M,OAAoBQ,EAC5B,CAED,SAASwgB,IACP,GAAIZ,EAEF,OADAE,GAAsB,EACftgB,OAAoBQ,GAG7B4f,GAAU,EAEV,IAAMzN,EAAcnF,GAA2C0S,EAAQ7b,2BAOvE,OANoB,OAAhBsO,EACFiO,IAEAK,EAAmBtO,EAAY/F,OAAQ,GAGlC5M,OAAoBQ,EAC5B,CAED,SAAS+gB,EAAiBrhB,GAGxB,GAFAqgB,GAAY,EACZR,EAAU7f,EACNsgB,EAAW,CACb,IAAMgB,EAAkB1Z,GAAoB,CAACiY,EAASC,IAChDyB,EAAexd,GAAqBV,EAAQie,GAClDrB,EAAqBsB,EACtB,CACD,OAAOhB,CACR,CAED,SAASiB,EAAiBxhB,GAGxB,GAFAsgB,GAAY,EACZR,EAAU9f,EACNqgB,EAAW,CACb,IAAMiB,EAAkB1Z,GAAoB,CAACiY,EAASC,IAChDyB,EAAexd,GAAqBV,EAAQie,GAClDrB,EAAqBsB,EACtB,CACD,OAAOhB,CACR,CAED,SAASvN,IAER,CAOD,OALA+M,EAAU0B,GAAyBzO,EAAgB6N,EAAgBQ,GACnErB,EAAUyB,GAAyBzO,EAAgB8N,EAAgBU,GAEnEhB,EAAmBpd,GAEZ,CAAC2c,EAASC,EACnB,CAnYW0B,CAAsBre,GAMjB,SACdA,EACAuc,GAKA,IAMIC,EACAC,EACAC,EACAC,EAEAC,EAXE7c,EAAS8C,EAAsC7C,GAEjD6c,GAAU,EACVyB,GAAY,EACZtB,GAAY,EACZC,GAAY,EAOVC,EAAgB3gB,GAAsB,SAAA3B,GAC1CgiB,EAAuBhiB,CACzB,IAEA,SAASgV,IACP,OAAIiN,GACFyB,GAAY,EACL7hB,OAAoBQ,KAG7B4f,GAAU,EAgDV9Y,GAAgChE,EA9CI,CAClCsD,YAAa,SAAAF,GAIXzF,GAAe,WACb4gB,GAAY,EACZ,IAAMhB,EAASna,EACToa,EAASpa,EAQV6Z,GACHjB,GAAuCW,EAAQ5b,0BAA2Bwc,GAEvEL,GACHlB,GAAuCY,EAAQ7b,0BAA2Byc,GAG5EV,GAAU,EACNyB,GACF1O,GAEJ,GACD,EACDxM,YAAa,WACXyZ,GAAU,EACLG,GACHlB,GAAqCY,EAAQ5b,2BAE1Cmc,GACHnB,GAAqCa,EAAQ7b,2BAG1Ckc,GAAcC,GACjBL,OAAqB3f,EAExB,EACD+G,YAAa,WACX6Y,GAAU,CACX,IAIIpgB,OAAoBQ,GAC5B,CAED,SAAS+gB,EAAiBrhB,GAGxB,GAFAqgB,GAAY,EACZR,EAAU7f,EACNsgB,EAAW,CACb,IAAMgB,EAAkB1Z,GAAoB,CAACiY,EAASC,IAChDyB,EAAexd,GAAqBV,EAAQie,GAClDrB,EAAqBsB,EACtB,CACD,OAAOhB,CACR,CAED,SAASiB,EAAiBxhB,GAGxB,GAFAsgB,GAAY,EACZR,EAAU9f,EACNqgB,EAAW,CACb,IAAMiB,EAAkB1Z,GAAoB,CAACiY,EAASC,IAChDyB,EAAexd,GAAqBV,EAAQie,GAClDrB,EAAqBsB,EACtB,CACD,OAAOhB,CACR,CAED,SAASvN,IAER,CAcD,OAZA+M,EAAU6B,GAAqB5O,EAAgBC,EAAeoO,GAC9DrB,EAAU4B,GAAqB5O,EAAgBC,EAAeuO,GAE9DhhB,EAAc4C,EAAOiB,gBAAgB,SAACrG,GAMpC,OALAqhB,GAAqCU,EAAQ5b,0BAA2BnG,GACxEqhB,GAAqCW,EAAQ7b,0BAA2BnG,GACnEqiB,GAAcC,GACjBL,OAAqB3f,GAEhB,IACT,IAEO,CAACyf,EAASC,EACnB,CA5HS6B,CAAyBxe,EAClC,CCxCM,SAAUye,GACd5E,GAEA,OCeOte,EAD+ByE,EDdb6Z,SCe6D,IAA/C7Z,EAAiC0e,UDiDpE,SACJ3e,GAEA,IAAIC,EAIJ,SAAS4P,IACP,IAAI+O,EACJ,IACEA,EAAc5e,EAAO6D,MACtB,CAAC,MAAOnK,GACP,OAAOiD,EAAoBjD,EAC5B,CACD,OAAO2D,EAAqBuhB,GAAa,SAAAC,GACvC,IAAKrjB,EAAaqjB,GAChB,MAAM,IAAI1lB,UAAU,gFAEtB,GAAI0lB,EAAWxlB,KACb0iB,GAAqC9b,EAAOc,+BACvC,CACL,IAAMzH,EAAQulB,EAAWvlB,MACzB0iB,GAAuC/b,EAAOc,0BAA2BzH,EAC1E,CACH,GACD,CAED,SAASwW,EAAgBlT,GACvB,IACE,OAAOF,EAAoBsD,EAAO4D,OAAOhH,GAC1C,CAAC,MAAOlD,GACP,OAAOiD,EAAoBjD,EAC5B,CACF,CAGD,OADAuG,EAASue,GA9BcjjB,EA8BuBsU,EAAeC,EAAiB,GACvE7P,CACT,CApGW6e,CAAgChF,EAAO6E,aAK5C,SAAwCI,GAC5C,IAAI9e,EACE+e,EAAiBtY,GAAYqY,EAAe,SAIlD,SAASlP,IACP,IAAIoP,EACJ,IACEA,ErBoIA,SAA0BD,GAC9B,IAAMpX,EAAS9J,EAAYkhB,EAAejY,WAAYiY,EAAepnB,SAAU,IAC/E,IAAK4D,EAAaoM,GAChB,MAAM,IAAIzO,UAAU,oDAEtB,OAAOyO,CACT,CqB1ImBsX,CAAaF,EAC3B,CAAC,MAAOtlB,GACP,OAAOiD,EAAoBjD,EAC5B,CAED,OAAO2D,EADaX,EAAoBuiB,IACC,SAAAE,GACvC,IAAK3jB,EAAa2jB,GAChB,MAAM,IAAIhmB,UAAU,kFAEtB,IAAME,ErBmIN,SACJ8lB,GAGA,OAAOC,QAAQD,EAAW9lB,KAC5B,CqBxImBgmB,CAAiBF,GAC9B,GAAI9lB,EACF0iB,GAAqC9b,EAAOc,+BACvC,CACL,IAAMzH,ErBsIR,SAA2B6lB,GAE/B,OAAOA,EAAW7lB,KACpB,CqBzIsBgmB,CAAcH,GAC5BnD,GAAuC/b,EAAOc,0BAA2BzH,EAC1E,CACH,GACD,CAED,SAASwW,EAAgBlT,GACvB,IACI2iB,EASAC,EAVE5nB,EAAWonB,EAAepnB,SAEhC,IACE2nB,EAAetZ,GAAUrO,EAAU,SACpC,CAAC,MAAO8B,GACP,OAAOiD,EAAoBjD,EAC5B,CACD,QAAqBwD,IAAjBqiB,EACF,OAAO7iB,OAAoBQ,GAG7B,IACEsiB,EAAe1hB,EAAYyhB,EAAc3nB,EAAU,CAACgF,GACrD,CAAC,MAAOlD,GACP,OAAOiD,EAAoBjD,EAC5B,CAED,OAAO2D,EADeX,EAAoB8iB,IACC,SAAAL,GACzC,IAAK3jB,EAAa2jB,GAChB,MAAM,IAAIhmB,UAAU,mFAGxB,GACD,CAGD,OADA8G,EAASue,GAlDcjjB,EAkDuBsU,EAAeC,EAAiB,GACvE7P,CACT,CA3DSwf,CAA2B3F,GCW9B,IAAkC7Z,CDVxC,CEyBA,SAASyf,GACP7jB,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAAClF,GAAgB,OAAAuB,EAAYtC,EAAIyV,EAAU,CAAC1U,IACrD,CAEA,SAAS+iB,GACP9jB,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAACgK,GAA4C,OAAA3N,EAAYtC,EAAIyV,EAAU,CAACxF,IACjF,CAEA,SAAS8T,GACP/jB,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAACgK,GAA4C,OAAAhO,EAAYjC,EAAIyV,EAAU,CAACxF,IACjF,CAEA,SAAS+T,GAA0BzN,EAActQ,GAE/C,GAAa,WADbsQ,EAAO,GAAAta,OAAGsa,IAER,MAAM,IAAIjZ,UAAU,GAAArB,OAAGgK,EAAY,MAAAhK,OAAAsa,EAA+D,8DAEpG,OAAOA,CACT,CCzEgB,SAAA0N,GAAmBvP,EACAzO,GACjCF,EAAiB2O,EAASzO,GAC1B,IAAMkY,EAAezJ,aAAA,EAAAA,EAASyJ,aACxB7S,EAAgBoJ,aAAA,EAAAA,EAASpJ,cACzB4S,EAAexJ,aAAA,EAAAA,EAASwJ,aACxBjC,EAASvH,aAAA,EAAAA,EAASuH,OAIxB,YAHe5a,IAAX4a,GAWN,SAA2BA,EAAiBhW,GAC1C,IVUI,SAAwBxI,GAC5B,GAAqB,iBAAVA,GAAgC,OAAVA,EAC/B,OAAO,EAET,IACE,MAAiD,kBAAlCA,EAAsBkhB,OACtC,CAAC,MAAAte,GAEA,OAAO,CACR,CACH,CUpBO6jB,CAAcjI,GACjB,MAAM,IAAI3e,UAAU,UAAG2I,EAAO,2BAElC,CAdIke,CAAkBlI,EAAQ,UAAGhW,EAAO,8BAE/B,CACLkY,aAAcoF,QAAQpF,GACtB7S,cAAeiY,QAAQjY,GACvB4S,aAAcqF,QAAQrF,GACtBjC,OAAMA,EAEV,CLuHA/b,OAAOkJ,iBAAiB0W,GAAgCtf,UAAW,CACjE+N,MAAO,CAAElF,YAAY,GACrBuF,QAAS,CAAEvF,YAAY,GACvByF,MAAO,CAAEzF,YAAY,GACrBiH,YAAa,CAAEjH,YAAY,KAE7BtJ,EAAgB+f,GAAgCtf,UAAU+N,MAAO,SACjExO,EAAgB+f,GAAgCtf,UAAUoO,QAAS,WACnE7O,EAAgB+f,GAAgCtf,UAAUsO,MAAO,SAC/B,iBAAvBhT,EAAOyN,aAChBrJ,OAAOC,eAAe2f,GAAgCtf,UAAW1E,EAAOyN,YAAa,CACnF9L,MAAO,kCACP2C,cAAc,IMhElB,IAAAgkB,GAAA,WAcE,SAAYA,eAAAC,EACAlO,QADA,IAAAkO,IAAAA,EAAuF,CAAA,QACvF,IAAAlO,IAAAA,EAAuD,CAAA,QACrC9U,IAAxBgjB,EACFA,EAAsB,KAEtBle,EAAake,EAAqB,mBAGpC,IAAMnP,EAAWG,GAAuBc,EAAa,oBAC/CmO,EFjGM,SACdrG,EACAhY,GAEAF,EAAiBkY,EAAQhY,GACzB,IAAMwP,EAAWwI,EACX7O,EAAwBqG,aAAA,EAAAA,EAAUrG,sBAClCrH,EAAS0N,aAAA,EAAAA,EAAU1N,OACnBwc,EAAO9O,aAAA,EAAAA,EAAU8O,KACjBjO,EAAQb,aAAA,EAAAA,EAAUa,MAClBC,EAAOd,aAAA,EAAAA,EAAUc,KACvB,MAAO,CACLnH,2BAAiD/N,IAA1B+N,OACrB/N,EACAsF,EACEyI,EACA,GAAGnT,OAAAgK,+CAEP8B,YAAmB1G,IAAX0G,OACN1G,EACAwiB,GAAsC9b,EAAQ0N,EAAW,GAAGxZ,OAAAgK,gCAC9Dse,UAAeljB,IAATkjB,OACJljB,EACAyiB,GAAoCS,EAAM9O,EAAW,GAAGxZ,OAAAgK,8BAC1DqQ,WAAiBjV,IAAViV,OACLjV,EACA0iB,GAAqCzN,EAAOb,EAAW,GAAGxZ,OAAAgK,+BAC5DsQ,UAAelV,IAATkV,OAAqBlV,EAAY2iB,GAA0BzN,EAAM,GAAGta,OAAAgK,8BAE9E,CEoE6Bue,CAAqCH,EAAqB,mBAInF,GAFAI,GAAyBvnB,MAEK,UAA1BonB,EAAiB/N,KAAkB,CACrC,QAAsBlV,IAAlB6T,EAASpI,KACX,MAAM,IAAIG,WAAW,wElBk9B3B7I,EACAsgB,EACAxQ,GAEA,IAEIH,EACAC,EACAC,EAJEhE,EAA2C/P,OAAOuT,OAAOvF,GAA6B1N,WAO1FuT,OADiC1S,IAA/BqjB,EAAqBpO,MACN,WAAM,OAAAoO,EAAqBpO,MAAOrG,IAElC,aAGjB+D,OADgC3S,IAA9BqjB,EAAqBH,KACP,WAAM,OAAAG,EAAqBH,KAAMtU,IAEjC,WAAM,OAAApP,OAAoBQ,EAApB,EAGtB4S,OADkC5S,IAAhCqjB,EAAqB3c,OACL,SAAAhH,GAAU,OAAA2jB,EAAqB3c,OAAQhH,IAEvC,WAAM,OAAAF,OAAoBQ,EAApB,EAG1B,IAAM+N,EAAwBsV,EAAqBtV,sBACnD,GAA8B,IAA1BA,EACF,MAAM,IAAI9R,UAAU,gDAGtBwW,GACE1P,EAAQ6L,EAAY8D,EAAgBC,EAAeC,EAAiBC,EAAe9E,EAEvF,CkBj/BMuV,CACEznB,KACAonB,EAHoBrP,GAAqBC,EAAU,GAMtD,KAAM,CAEL,IAAMyB,EAAgBvB,GAAqBF,IN+P3C,SACJ9Q,EACAkgB,EACApQ,EACAyC,GAEA,IAEI5C,EACAC,EACAC,EAJEhE,EAAiD/P,OAAOuT,OAAOqM,GAAgCtf,WAOnGuT,OAD6B1S,IAA3BijB,EAAiBhO,MACF,WAAM,OAAAgO,EAAiBhO,MAAOrG,IAE9B,aAGjB+D,OAD4B3S,IAA1BijB,EAAiBC,KACH,WAAM,OAAAD,EAAiBC,KAAMtU,IAE7B,WAAM,OAAApP,OAAoBQ,EAApB,EAGtB4S,OAD8B5S,IAA5BijB,EAAiBvc,OACD,SAAAhH,GAAU,OAAAujB,EAAiBvc,OAAQhH,IAEnC,WAAM,OAAAF,OAAoBQ,EAApB,EAG1Bof,GACErc,EAAQ6L,EAAY8D,EAAgBC,EAAeC,EAAiBC,EAAeyC,EAEvF,CM5RMiO,CACE1nB,KACAonB,EAHoBrP,GAAqBC,EAAU,GAKnDyB,EAEH,CACF,CAoNH,OA/MEzW,OAAAC,eAAIikB,eAAM5jB,UAAA,SAAA,CAAVsC,IAAA,WACE,IAAKkE,GAAiB9J,MACpB,MAAMga,GAA0B,UAGlC,OAAOrP,GAAuB3K,KAC/B,kCAQDknB,eAAM5jB,UAAAuH,OAAN,SAAOhH,GACL,YADK,IAAAA,IAAAA,OAAuBM,GACvB2F,GAAiB9J,MAIlB2K,GAAuB3K,MAClB4D,EAAoB,IAAIxD,UAAU,qDAGpCwH,GAAqB5H,KAAM6D,GAPzBD,EAAoBoW,GAA0B,YA6BzDkN,eAAS5jB,UAAAsiB,UAAT,SACErO,GAEA,QAFA,IAAAA,IAAAA,OAAyEpT,IAEpE2F,GAAiB9J,MACpB,MAAMga,GAA0B,aAKlC,YAAqB7V,IhB3LT,SAAqBqT,EACAzO,GACnCF,EAAiB2O,EAASzO,GAC1B,IAAMmO,EAAOM,aAAA,EAAAA,EAASN,KACtB,MAAO,CACLA,UAAe/S,IAAT+S,OAAqB/S,EAAY8S,GAAgCC,EAAM,GAAGnY,OAAAgK,8BAEpF,CgBkLoB4e,CAAqBpQ,EAAY,mBAErCL,KACHnN,EAAmC/J,MAIrCmX,GAAgCnX,OAczCknB,eAAA5jB,UAAAskB,YAAA,SACEC,EACAtQ,GAEA,QAFA,IAAAA,IAAAA,EAAqD,CAAA,IAEhDzN,GAAiB9J,MACpB,MAAMga,GAA0B,eAElC7Q,EAAuB0e,EAAc,EAAG,eAExC,IAAMC,ECxNM,SACdrY,EACA1G,GAEAF,EAAiB4G,EAAM1G,GAEvB,IAAMgf,EAAWtY,aAAA,EAAAA,EAAMsY,SACvB1e,EAAoB0e,EAAU,WAAY,wBAC1Cle,EAAqBke,EAAU,UAAGhf,EAAO,gCAEzC,IAAM6X,EAAWnR,aAAA,EAAAA,EAAMmR,SAIvB,OAHAvX,EAAoBuX,EAAU,WAAY,wBAC1CjI,GAAqBiI,EAAU,UAAG7X,EAAO,gCAElC,CAAEgf,SAAQA,EAAEnH,SAAQA,EAC7B,CDyMsBoH,CAA4BH,EAAc,mBACtDrQ,EAAUuP,GAAmBxP,EAAY,oBAE/C,GAAI5M,GAAuB3K,MACzB,MAAM,IAAII,UAAU,kFAEtB,GAAI6Z,GAAuB6N,EAAUlH,UACnC,MAAM,IAAIxgB,UAAU,kFAStB,OAFAqE,EAJgBqc,GACd9gB,KAAM8nB,EAAUlH,SAAUpJ,EAAQwJ,aAAcxJ,EAAQyJ,aAAczJ,EAAQpJ,cAAeoJ,EAAQuH,SAKhG+I,EAAUC,UAWnBb,eAAA5jB,UAAA2kB,OAAA,SAAOC,EACA3Q,GACL,QADK,IAAAA,IAAAA,EAAqD,CAAA,IACrDzN,GAAiB9J,MACpB,OAAO4D,EAAoBoW,GAA0B,WAGvD,QAAoB7V,IAAhB+jB,EACF,OAAOtkB,EAAoB,wCAE7B,IAAKgV,GAAiBsP,GACpB,OAAOtkB,EACL,IAAIxD,UAAU,8EAIlB,IAAIoX,EACJ,IACEA,EAAUuP,GAAmBxP,EAAY,mBAC1C,CAAC,MAAO5W,GACP,OAAOiD,EAAoBjD,EAC5B,CAED,OAAIgK,GAAuB3K,MAClB4D,EACL,IAAIxD,UAAU,8EAGd6Z,GAAuBiO,GAClBtkB,EACL,IAAIxD,UAAU,8EAIX0gB,GACL9gB,KAAMkoB,EAAa1Q,EAAQwJ,aAAcxJ,EAAQyJ,aAAczJ,EAAQpJ,cAAeoJ,EAAQuH,SAelGmI,eAAA5jB,UAAA6kB,IAAA,WACE,IAAKre,GAAiB9J,MACpB,MAAMga,GAA0B,OAIlC,OAAOvO,GADU+X,GAAkBxjB,QAgBrCknB,eAAM5jB,UAAA8kB,OAAN,SAAO7Q,GACL,QADK,IAAAA,IAAAA,OAAwEpT,IACxE2F,GAAiB9J,MACpB,MAAMga,GAA0B,UAGlC,IvBlLkD9S,EACAkH,EAC9CnH,EACAohB,EACAxpB,EuB8KE2Y,EE9TM,SAAuBA,EACAzO,GACrCF,EAAiB2O,EAASzO,GAC1B,IAAMqF,EAAgBoJ,aAAA,EAAAA,EAASpJ,cAC/B,MAAO,CAAEA,cAAeiY,QAAQjY,GAClC,CFyToBka,CAAuB/Q,EAAY,mBACnD,OvBnLkDrQ,EuBmLLlH,KvBlLKoO,EuBkLCoJ,EAAQpJ,cvBjLvDnH,EAAS8C,EAAsC7C,GAC/CmhB,EAAO,IAAIla,GAAgClH,EAAQmH,IACnDvP,EAAmDmE,OAAOuT,OAAOzH,KAC9DE,mBAAqBqZ,EACvBxpB,GuBqLPqoB,eAAA5jB,UAACiK,IAAD,SAAsBiK,GAEpB,OAAOxX,KAAKooB,OAAO5Q,IASd0P,eAAIqB,KAAX,SAAevC,GACb,OAAOL,GAAmBK,IAE7BkB,cAAD,IAuDM,SAAUzB,GACd5O,EACAC,EACAC,EACAC,EACAyC,QADA,IAAAzC,IAAAA,EAAiB,QACjB,IAAAyC,IAAAA,EAAA,WAAsD,OAAA,IAItD,IAAMvS,EAAmClE,OAAOuT,OAAO2Q,GAAe5jB,WAQtE,OAPAikB,GAAyBrgB,GAGzBqc,GACErc,EAFqDlE,OAAOuT,OAAOqM,GAAgCtf,WAE/EuT,EAAgBC,EAAeC,EAAiBC,EAAeyC,GAG9EvS,CACT,UAGgBoe,GACdzO,EACAC,EACAC,GAEA,IAAM7P,EAA6BlE,OAAOuT,OAAO2Q,GAAe5jB,WAMhE,OALAikB,GAAyBrgB,GAGzB0P,GAAkC1P,EADelE,OAAOuT,OAAOvF,GAA6B1N,WACtCuT,EAAgBC,EAAeC,EAAiB,OAAG5S,GAElG+C,CACT,CAEA,SAASqgB,GAAyBrgB,GAChCA,EAAOG,OAAS,WAChBH,EAAOE,aAAUjD,EACjB+C,EAAOQ,kBAAevD,EACtB+C,EAAOqE,YAAa,CACtB,CAEM,SAAUzB,GAAiBpH,GAC/B,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,8BAItCA,aAAawkB,GACtB,CAQM,SAAUvc,GAAuBzD,GAGrC,YAAuB/C,IAAnB+C,EAAOE,OAKb,CAIgB,SAAAQ,GAAwBV,EAA2BrD,GAGjE,GAFAqD,EAAOqE,YAAa,EAEE,WAAlBrE,EAAOG,OACT,OAAO1D,OAAoBQ,GAE7B,GAAsB,YAAlB+C,EAAOG,OACT,OAAOzD,EAAoBsD,EAAOQ,cAGpC0N,GAAoBlO,GAEpB,IAAMD,EAASC,EAAOE,QACtB,QAAejD,IAAX8C,GAAwBoQ,GAA2BpQ,GAAS,CAC9D,IAAM6Q,EAAmB7Q,EAAO6M,kBAChC7M,EAAO6M,kBAAoB,IAAIzO,EAC/ByS,EAAiBxR,SAAQ,SAAAuN,GACvBA,EAAgBvJ,iBAAYnG,EAC9B,GACD,CAGD,OAAOG,EADqB4C,EAAOc,0BAA0BnB,GAAahD,GACzBrB,EACnD,CAEM,SAAU4S,GAAuBlO,GAGrCA,EAAOG,OAAS,SAEhB,IAAMJ,EAASC,EAAOE,QAEtB,QAAejD,IAAX8C,IAIJM,EAAkCN,GAE9ByD,GAAiCzD,IAAS,CAC5C,IAAMuE,EAAevE,EAAOkD,cAC5BlD,EAAOkD,cAAgB,IAAI9E,EAC3BmG,EAAalF,SAAQ,SAAA4D,GACnBA,EAAYI,aACd,GACD,CACH,CAEgB,SAAA8L,GAAuBlP,EAA2BvG,GAIhEuG,EAAOG,OAAS,UAChBH,EAAOQ,aAAe/G,EAEtB,IAAMsG,EAASC,EAAOE,aAEPjD,IAAX8C,IAIJa,EAAiCb,EAAQtG,GAErC+J,GAAiCzD,GACnCmE,GAA6CnE,EAAQtG,GAGrDiX,GAA8C3Q,EAAQtG,GAE1D,CAqBA,SAASqZ,GAA0BjX,GACjC,OAAO,IAAI3C,UAAU,mCAA4B2C,EAAI,yCACvD,CGljBgB,SAAAylB,GAA2BpQ,EACArP,GACzCF,EAAiBuP,EAAMrP,GACvB,IAAMiO,EAAgBoB,aAAA,EAAAA,EAAMpB,cAE5B,OADA3N,EAAoB2N,EAAe,gBAAiB,uBAC7C,CACLA,cAAezN,EAA0ByN,GAE7C,CHkVAhU,OAAOkJ,iBAAiBgb,GAAgB,CACtCqB,KAAM,CAAEpc,YAAY,KAEtBnJ,OAAOkJ,iBAAiBgb,GAAe5jB,UAAW,CAChDuH,OAAQ,CAAEsB,YAAY,GACtByZ,UAAW,CAAEzZ,YAAY,GACzByb,YAAa,CAAEzb,YAAY,GAC3B8b,OAAQ,CAAE9b,YAAY,GACtBgc,IAAK,CAAEhc,YAAY,GACnBic,OAAQ,CAAEjc,YAAY,GACtBwQ,OAAQ,CAAExQ,YAAY,KAExBtJ,EAAgBqkB,GAAeqB,KAAM,QACrC1lB,EAAgBqkB,GAAe5jB,UAAUuH,OAAQ,UACjDhI,EAAgBqkB,GAAe5jB,UAAUsiB,UAAW,aACpD/iB,EAAgBqkB,GAAe5jB,UAAUskB,YAAa,eACtD/kB,EAAgBqkB,GAAe5jB,UAAU2kB,OAAQ,UACjDplB,EAAgBqkB,GAAe5jB,UAAU6kB,IAAK,OAC9CtlB,EAAgBqkB,GAAe5jB,UAAU8kB,OAAQ,UACf,iBAAvBxpB,EAAOyN,aAChBrJ,OAAOC,eAAeikB,GAAe5jB,UAAW1E,EAAOyN,YAAa,CAClE9L,MAAO,iBACP2C,cAAc,IAGlBF,OAAOC,eAAeikB,GAAe5jB,UAAWiK,GAAqB,CACnEhN,MAAO2mB,GAAe5jB,UAAU8kB,OAChCxH,UAAU,EACV1d,cAAc,IInXhB,IAAMulB,GAAyB,SAACpe,GAC9B,OAAOA,EAAMwC,UACf,EACAhK,EAAgB4lB,GAAwB,QAOxC,IAAAC,GAAA,WAIE,SAAAA,0BAAYlR,GACVrO,EAAuBqO,EAAS,EAAG,6BACnCA,EAAUgR,GAA2BhR,EAAS,mBAC9CxX,KAAK2oB,wCAA0CnR,EAAQR,aACxD,CAqBH,OAhBEhU,OAAAC,eAAIylB,0BAAaplB,UAAA,gBAAA,CAAjBsC,IAAA,WACE,IAAKgjB,GAA4B5oB,MAC/B,MAAM6oB,GAA8B,iBAEtC,OAAO7oB,KAAK2oB,uCACb,kCAKD3lB,OAAAC,eAAIylB,0BAAIplB,UAAA,OAAA,CAARsC,IAAA,WACE,IAAKgjB,GAA4B5oB,MAC/B,MAAM6oB,GAA8B,QAEtC,OAAOJ,EACR,kCACFC,yBAAD,IAeA,SAASG,GAA8B9lB,GACrC,OAAO,IAAI3C,UAAU,8CAAuC2C,EAAI,oDAClE,CAEM,SAAU6lB,GAA4BlmB,GAC1C,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,4CAItCA,aAAagmB,GACtB,CA3BA1lB,OAAOkJ,iBAAiBwc,GAA0BplB,UAAW,CAC3D0T,cAAe,CAAE7K,YAAY,GAC7ByD,KAAM,CAAEzD,YAAY,KAEY,iBAAvBvN,EAAOyN,aAChBrJ,OAAOC,eAAeylB,GAA0BplB,UAAW1E,EAAOyN,YAAa,CAC7E9L,MAAO,4BACP2C,cAAc,IChDlB,IAAM4lB,GAAoB,WACxB,OAAO,CACT,EACAjmB,EAAgBimB,GAAmB,QAOnC,IAAAC,GAAA,WAIE,SAAAA,qBAAYvR,GACVrO,EAAuBqO,EAAS,EAAG,wBACnCA,EAAUgR,GAA2BhR,EAAS,mBAC9CxX,KAAKgpB,mCAAqCxR,EAAQR,aACnD,CAsBH,OAjBEhU,OAAAC,eAAI8lB,qBAAazlB,UAAA,gBAAA,CAAjBsC,IAAA,WACE,IAAKqjB,GAAuBjpB,MAC1B,MAAMkpB,GAAyB,iBAEjC,OAAOlpB,KAAKgpB,kCACb,kCAMDhmB,OAAAC,eAAI8lB,qBAAIzlB,UAAA,OAAA,CAARsC,IAAA,WACE,IAAKqjB,GAAuBjpB,MAC1B,MAAMkpB,GAAyB,QAEjC,OAAOJ,EACR,kCACFC,oBAAD,IAeA,SAASG,GAAyBnmB,GAChC,OAAO,IAAI3C,UAAU,yCAAkC2C,EAAI,+CAC7D,CAEM,SAAUkmB,GAAuBvmB,GACrC,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,uCAItCA,aAAaqmB,GACtB,CCpCA,SAASI,GACPrmB,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAACgK,GAAoD,OAAA3N,EAAYtC,EAAIyV,EAAU,CAACxF,IACzF,CAEA,SAASqW,GACPtmB,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAACgK,GAAoD,OAAAhO,EAAYjC,EAAIyV,EAAU,CAACxF,IACzF,CAEA,SAASsW,GACPvmB,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAACsB,EAAU0I,GAAoD,OAAA3N,EAAYtC,EAAIyV,EAAU,CAAClO,EAAO0I,GAAY,CACtH,CAEA,SAASuW,GACPxmB,EACAyV,EACAxP,GAGA,OADAC,EAAelG,EAAIiG,GACZ,SAAClF,GAAgB,OAAAuB,EAAYtC,EAAIyV,EAAU,CAAC1U,IACrD,CDzBAb,OAAOkJ,iBAAiB6c,GAAqBzlB,UAAW,CACtD0T,cAAe,CAAE7K,YAAY,GAC7ByD,KAAM,CAAEzD,YAAY,KAEY,iBAAvBvN,EAAOyN,aAChBrJ,OAAOC,eAAe8lB,GAAqBzlB,UAAW1E,EAAOyN,YAAa,CACxE9L,MAAO,uBACP2C,cAAc,IEXlB,IAAAqmB,GAAA,WAmBE,SAAAA,gBAAYC,EACAC,EACAC,QAFA,IAAAF,IAAAA,EAAyD,CAAA,QACzD,IAAAC,IAAAA,EAA+D,CAAA,QAC/D,IAAAC,IAAAA,EAA+D,CAAA,QAClDvlB,IAAnBqlB,IACFA,EAAiB,MAGnB,IAAMG,EAAmBxR,GAAuBsR,EAAqB,oBAC/DG,EAAmBzR,GAAuBuR,EAAqB,mBAE/DG,ED7DM,SAAyBtR,EACAxP,GACvCF,EAAiB0P,EAAUxP,GAC3B,IAAM8B,EAAS0N,aAAA,EAAAA,EAAU1N,OACnBif,EAAQvR,aAAA,EAAAA,EAAUuR,MAClBC,EAAexR,aAAA,EAAAA,EAAUwR,aACzB3Q,EAAQb,aAAA,EAAAA,EAAUa,MAClB0O,EAAYvP,aAAA,EAAAA,EAAUuP,UACtBkC,EAAezR,aAAA,EAAAA,EAAUyR,aAC/B,MAAO,CACLnf,YAAmB1G,IAAX0G,OACN1G,EACAmlB,GAAiCze,EAAQ0N,EAAW,GAAGxZ,OAAAgK,gCACzD+gB,WAAiB3lB,IAAV2lB,OACL3lB,EACAglB,GAAgCW,EAAOvR,EAAW,GAAGxZ,OAAAgK,+BACvDghB,aAAYA,EACZ3Q,WAAiBjV,IAAViV,OACLjV,EACAilB,GAAgChQ,EAAOb,EAAW,GAAGxZ,OAAAgK,+BACvD+e,eAAyB3jB,IAAd2jB,OACT3jB,EACAklB,GAAoCvB,EAAWvP,EAAW,GAAGxZ,OAAAgK,mCAC/DihB,aAAYA,EAEhB,CCoCwBC,CAAmBT,EAAgB,mBACvD,QAAiCrlB,IAA7B0lB,EAAYE,aACd,MAAM,IAAIha,WAAW,kCAEvB,QAAiC5L,IAA7B0lB,EAAYG,aACd,MAAM,IAAIja,WAAW,kCAGvB,IAKIma,EALEC,EAAwBpS,GAAqB6R,EAAkB,GAC/DQ,EAAwBlS,GAAqB0R,GAC7CS,EAAwBtS,GAAqB4R,EAAkB,GAC/DW,EAAwBpS,GAAqByR,IA6FvD,SAAyCziB,EACAqjB,EACAF,EACAC,EACAH,EACAC,GACvC,SAASvT,IACP,OAAO0T,CACR,CAED,SAAS7Q,EAAerP,GACtB,OA6SJ,SAAwDnD,EAA+BmD,GAGrF,IAAM0I,EAAa7L,EAAOsjB,2BAE1B,GAAItjB,EAAO6T,cAAe,CAGxB,OAAOzW,EAF2B4C,EAAOujB,4BAEc,WACrD,IAAM7J,EAAW1Z,EAAOwjB,UAExB,GAAc,aADA9J,EAASvZ,OAErB,MAAMuZ,EAASlZ,aAGjB,OAAOijB,GAAuD5X,EAAY1I,EAC5E,GACD,CAED,OAAOsgB,GAAuD5X,EAAY1I,EAC5E,CAjUWugB,CAAyC1jB,EAAQmD,EACzD,CAED,SAASuP,EAAe/V,GACtB,OA+TJ,SAAwDqD,EAA+BrD,GACrF,IAAMkP,EAAa7L,EAAOsjB,2BAC1B,QAAkCrmB,IAA9B4O,EAAW8X,eACb,OAAO9X,EAAW8X,eAIpB,IAAM9C,EAAW7gB,EAAO4jB,UAIxB/X,EAAW8X,eAAiBpnB,GAAW,SAAC3B,EAASG,GAC/C8Q,EAAWgY,uBAAyBjpB,EACpCiR,EAAWiY,sBAAwB/oB,CACrC,IAEA,IAAMmiB,EAAgBrR,EAAWhB,iBAAiBlO,GAiBlD,OAhBAonB,GAAgDlY,GAEhD7O,EAAYkgB,GAAe,WAOzB,MANwB,YAApB2D,EAAS1gB,OACX6jB,GAAqCnY,EAAYgV,EAASrgB,eAE1Dwb,GAAqC6E,EAAS/f,0BAA2BnE,GACzEsnB,GAAsCpY,IAEjC,IACR,IAAE,SAAAlR,GAGD,OAFAqhB,GAAqC6E,EAAS/f,0BAA2BnG,GACzEqpB,GAAqCnY,EAAYlR,GAC1C,IACT,IAEOkR,EAAW8X,cACpB,CAjWWO,CAAyClkB,EAAQrD,EACzD,CAED,SAAS8V,IACP,OA+VJ,SAAwDzS,GACtD,IAAM6L,EAAa7L,EAAOsjB,2BAC1B,QAAkCrmB,IAA9B4O,EAAW8X,eACb,OAAO9X,EAAW8X,eAIpB,IAAM9C,EAAW7gB,EAAO4jB,UAIxB/X,EAAW8X,eAAiBpnB,GAAW,SAAC3B,EAASG,GAC/C8Q,EAAWgY,uBAAyBjpB,EACpCiR,EAAWiY,sBAAwB/oB,CACrC,IAEA,IAAMopB,EAAetY,EAAWuY,kBAiBhC,OAhBAL,GAAgDlY,GAEhD7O,EAAYmnB,GAAc,WAOxB,MANwB,YAApBtD,EAAS1gB,OACX6jB,GAAqCnY,EAAYgV,EAASrgB,eAE1Dsb,GAAqC+E,EAAS/f,2BAC9CmjB,GAAsCpY,IAEjC,IACR,IAAE,SAAAlR,GAGD,OAFAqhB,GAAqC6E,EAAS/f,0BAA2BnG,GACzEqpB,GAAqCnY,EAAYlR,GAC1C,IACT,IAEOkR,EAAW8X,cACpB,CAjYWU,CAAyCrkB,EACjD,CAKD,SAAS4P,IACP,OA8XJ,SAAmD5P,GASjD,OAHAskB,GAA+BtkB,GAAQ,GAGhCA,EAAOujB,0BAChB,CAxYWgB,CAA0CvkB,EAClD,CAED,SAAS6P,EAAgBlT,GACvB,OAsYJ,SAA2DqD,EAA+BrD,GACxF,IAAMkP,EAAa7L,EAAOsjB,2BAC1B,QAAkCrmB,IAA9B4O,EAAW8X,eACb,OAAO9X,EAAW8X,eAIpB,IAAMjK,EAAW1Z,EAAOwjB,UAKxB3X,EAAW8X,eAAiBpnB,GAAW,SAAC3B,EAASG,GAC/C8Q,EAAWgY,uBAAyBjpB,EACpCiR,EAAWiY,sBAAwB/oB,CACrC,IAEA,IAAMmiB,EAAgBrR,EAAWhB,iBAAiBlO,GAmBlD,OAlBAonB,GAAgDlY,GAEhD7O,EAAYkgB,GAAe,WAQzB,MAPwB,YAApBxD,EAASvZ,OACX6jB,GAAqCnY,EAAY6N,EAASlZ,eAE1D4W,GAA6CsC,EAASnG,0BAA2B5W,GACjF6nB,GAA4BxkB,GAC5BikB,GAAsCpY,IAEjC,IACR,IAAE,SAAAlR,GAID,OAHAyc,GAA6CsC,EAASnG,0BAA2B5Y,GACjF6pB,GAA4BxkB,GAC5BgkB,GAAqCnY,EAAYlR,GAC1C,IACT,IAEOkR,EAAW8X,cACpB,CA3aWc,CAA4CzkB,EAAQrD,EAC5D,CATDqD,EAAOwjB,UjBwBT,SAAiC7T,EACA6C,EACAC,EACAC,EACA5C,EACAyC,QADA,IAAAzC,IAAAA,EAAiB,QACjB,IAAAyC,IAAAA,EAAA,WAAsD,OAAA,IAGrF,IAAMvS,EAA4BlE,OAAOuT,OAAOwC,GAAezV,WAO/D,OANAkW,GAAyBtS,GAIzB4S,GAAqC5S,EAFkBlE,OAAOuT,OAAOsD,GAAgCvW,WAE5CuT,EAAgB6C,EAAgBC,EACpDC,EAAgB5C,EAAeyC,GAC7DvS,CACT,CiBxCqB0kB,CAAqB/U,EAAgB6C,EAAgBC,EAAgBC,EAChDyQ,EAAuBC,GAU/DpjB,EAAO4jB,UAAYrF,GAAqB5O,EAAgBC,EAAeC,EAAiBoT,EAChDC,GAGxCljB,EAAO6T,mBAAgB5W,EACvB+C,EAAOujB,gCAA6BtmB,EACpC+C,EAAO2kB,wCAAqC1nB,EAC5CqnB,GAA+BtkB,GAAQ,GAEvCA,EAAOsjB,gCAA6BrmB,CACtC,CAjII2nB,CACE9rB,KALmByD,GAAiB,SAAA3B,GACpCooB,EAAuBpoB,CACzB,IAGsBuoB,EAAuBC,EAAuBH,EAAuBC,GAgT/F,SAAoEljB,EACA2iB,GAClE,IAEIkC,EACAC,EACAjV,EAJEhE,EAAkD/P,OAAOuT,OAAO0V,GAAiC3oB,WAOrGyoB,OAD4B5nB,IAA1B0lB,EAAY/B,UACO,SAAAzd,GAAS,OAAAwf,EAAY/B,UAAWzd,EAAO0I,IAEvC,SAAA1I,GACnB,IAEE,OADA6hB,GAAwCnZ,EAAY1I,GAC7C1G,OAAoBQ,EAC5B,CAAC,MAAOgoB,GACP,OAAOvoB,EAAoBuoB,EAC5B,CACH,EAIAH,OADwB7nB,IAAtB0lB,EAAYC,MACG,WAAM,OAAAD,EAAYC,MAAO/W,IAEzB,WAAM,OAAApP,OAAoBQ,EAApB,EAIvB4S,OADyB5S,IAAvB0lB,EAAYhf,OACI,SAAAhH,GAAU,OAAAgmB,EAAYhf,OAAQhH,IAE9B,WAAM,OAAAF,OAAoBQ,EAApB,GAlD5B,SAAqD+C,EACA6L,EACAgZ,EACAC,EACAjV,GAInDhE,EAAWqZ,2BAA6BllB,EACxCA,EAAOsjB,2BAA6BzX,EAEpCA,EAAWsZ,oBAAsBN,EACjChZ,EAAWuY,gBAAkBU,EAC7BjZ,EAAWhB,iBAAmBgF,EAE9BhE,EAAW8X,oBAAiB1mB,EAC5B4O,EAAWgY,4BAAyB5mB,EACpC4O,EAAWiY,2BAAwB7mB,CACrC,CAmCEmoB,CAAsCplB,EAAQ6L,EAAYgZ,EAAoBC,EAAgBjV,EAChG,CAhVIwV,CAAqDvsB,KAAM6pB,QAEjC1lB,IAAtB0lB,EAAYzQ,MACd8Q,EAAqBL,EAAYzQ,MAAMpZ,KAAKwqB,6BAE5CN,OAAqB/lB,EAExB,CAuBH,OAlBEnB,OAAAC,eAAIsmB,gBAAQjmB,UAAA,WAAA,CAAZsC,IAAA,WACE,IAAK4mB,GAAkBxsB,MACrB,MAAMga,GAA0B,YAGlC,OAAOha,KAAK8qB,SACb,kCAKD9nB,OAAAC,eAAIsmB,gBAAQjmB,UAAA,WAAA,CAAZsC,IAAA,WACE,IAAK4mB,GAAkBxsB,MACrB,MAAMga,GAA0B,YAGlC,OAAOha,KAAK0qB,SACb,kCACFnB,eAAD,IAkGA,SAASiD,GAAkB9pB,GACzB,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,+BAItCA,aAAa6mB,GACtB,CAGA,SAASkD,GAAqBvlB,EAAyBvG,GACrDuiB,GAAqChc,EAAO4jB,UAAU9iB,0BAA2BrH,GACjF+rB,GAA4CxlB,EAAQvG,EACtD,CAEA,SAAS+rB,GAA4CxlB,EAAyBvG,GAC5EsqB,GAAgD/jB,EAAOsjB,4BACvDlM,GAA6CpX,EAAOwjB,UAAUjQ,0BAA2B9Z,GACzF+qB,GAA4BxkB,EAC9B,CAEA,SAASwkB,GAA4BxkB,GAC/BA,EAAO6T,eAITyQ,GAA+BtkB,GAAQ,EAE3C,CAEA,SAASskB,GAA+BtkB,EAAyBsV,QAIrBrY,IAAtC+C,EAAOujB,4BACTvjB,EAAO2kB,qCAGT3kB,EAAOujB,2BAA6BhnB,GAAW,SAAA3B,GAC7CoF,EAAO2kB,mCAAqC/pB,CAC9C,IAEAoF,EAAO6T,cAAgByB,CACzB,CA9IAxZ,OAAOkJ,iBAAiBqd,GAAgBjmB,UAAW,CACjDykB,SAAU,CAAE5b,YAAY,GACxByU,SAAU,CAAEzU,YAAY,KAEQ,iBAAvBvN,EAAOyN,aAChBrJ,OAAOC,eAAesmB,GAAgBjmB,UAAW1E,EAAOyN,YAAa,CACnE9L,MAAO,kBACP2C,cAAc,IAgJlB,IAAA+oB,GAAA,WAgBE,SAAAA,mCACE,MAAM,IAAI7rB,UAAU,sBACrB,CAiDH,OA5CE4C,OAAAC,eAAIgpB,iCAAW3oB,UAAA,cAAA,CAAfsC,IAAA,WACE,IAAK+mB,GAAmC3sB,MACtC,MAAM8e,GAAqC,eAI7C,OAAOgE,GADoB9iB,KAAKosB,2BAA2BtB,UAAU9iB,0BAEtE,kCAMDikB,iCAAO3oB,UAAAoO,QAAP,SAAQrH,GACN,QADM,IAAAA,IAAAA,OAAWlG,IACZwoB,GAAmC3sB,MACtC,MAAM8e,GAAqC,WAG7CoN,GAAwClsB,KAAMqK,IAOhD4hB,iCAAK3oB,UAAAsO,MAAL,SAAM/N,GACJ,QADI,IAAAA,IAAAA,OAAuBM,IACtBwoB,GAAmC3sB,MACtC,MAAM8e,GAAqC,SAyIjD,IAAkGne,IAtIlDkD,EAuI9C4oB,GAvIwCzsB,KAuIRosB,2BAA4BzrB,IAhI5DsrB,iCAAA3oB,UAAAspB,UAAA,WACE,IAAKD,GAAmC3sB,MACtC,MAAM8e,GAAqC,cA0IjD,SAAsD/L,GACpD,IAAM7L,EAAS6L,EAAWqZ,2BAG1BpJ,GAF2B9b,EAAO4jB,UAAU9iB,2BAI5C,IAAM4J,EAAQ,IAAIxR,UAAU,8BAC5BssB,GAA4CxlB,EAAQ0K,EACtD,CA/IIib,CAA0C7sB,OAE7CisB,gCAAD,IAoBA,SAASU,GAA4CjqB,GACnD,QAAKD,EAAaC,OAIbM,OAAOM,UAAUgI,eAAejL,KAAKqC,EAAG,+BAItCA,aAAaupB,GACtB,CA0DA,SAAShB,GAAgDlY,GACvDA,EAAWsZ,yBAAsBloB,EACjC4O,EAAWuY,qBAAkBnnB,EAC7B4O,EAAWhB,sBAAmB5N,CAChC,CAEA,SAAS+nB,GAA2CnZ,EAAiD1I,GACnG,IAAMnD,EAAS6L,EAAWqZ,2BACpBU,EAAqB5lB,EAAO4jB,UAAU9iB,0BAC5C,IAAK+a,GAAiD+J,GACpD,MAAM,IAAI1sB,UAAU,wDAMtB,IACE6iB,GAAuC6J,EAAoBziB,EAC5D,CAAC,MAAO1J,GAIP,MAFA+rB,GAA4CxlB,EAAQvG,GAE9CuG,EAAO4jB,UAAUpjB,YACxB,CAED,IAAM8U,EbjJF,SACJzJ,GAEA,OAAIuQ,GAA8CvQ,EAKpD,CayIuBga,CAA+CD,GAChEtQ,IAAiBtV,EAAO6T,eAE1ByQ,GAA+BtkB,GAAQ,EAE3C,CAMA,SAASyjB,GAAuD5X,EACA1I,GAE9D,OAAO/F,EADkByO,EAAWsZ,oBAAoBhiB,QACVlG,GAAW,SAAAtC,GAEvD,MADA4qB,GAAqB1Z,EAAWqZ,2BAA4BvqB,GACtDA,CACR,GACF,CAmKA,SAASid,GAAqC/b,GAC5C,OAAO,IAAI3C,UACT,qDAA8C2C,EAAI,2DACtD,CAEM,SAAUooB,GAAsCpY,QACV5O,IAAtC4O,EAAWgY,yBAIfhY,EAAWgY,yBACXhY,EAAWgY,4BAAyB5mB,EACpC4O,EAAWiY,2BAAwB7mB,EACrC,CAEgB,SAAA+mB,GAAqCnY,EAAmDlP,QAC7DM,IAArC4O,EAAWiY,wBAIfvmB,EAA0BsO,EAAW8X,gBACrC9X,EAAWiY,sBAAsBnnB,GACjCkP,EAAWgY,4BAAyB5mB,EACpC4O,EAAWiY,2BAAwB7mB,EACrC,CAIA,SAAS6V,GAA0BjX,GACjC,OAAO,IAAI3C,UACT,oCAA6B2C,EAAI,0CACrC,CAnUAC,OAAOkJ,iBAAiB+f,GAAiC3oB,UAAW,CAClEoO,QAAS,CAAEvF,YAAY,GACvByF,MAAO,CAAEzF,YAAY,GACrBygB,UAAW,CAAEzgB,YAAY,GACzBiH,YAAa,CAAEjH,YAAY,KAE7BtJ,EAAgBopB,GAAiC3oB,UAAUoO,QAAS,WACpE7O,EAAgBopB,GAAiC3oB,UAAUsO,MAAO,SAClE/O,EAAgBopB,GAAiC3oB,UAAUspB,UAAW,aACpC,iBAAvBhuB,EAAOyN,aAChBrJ,OAAOC,eAAegpB,GAAiC3oB,UAAW1E,EAAOyN,YAAa,CACpF9L,MAAO,mCACP2C,cAAc,IClVlB,IAAM8pB,GAAU,CACd9F,eAAcA,GACdtE,gCAA+BA,GAC/B5R,6BAA4BA,GAC5BZ,0BAAyBA,GACzBpG,4BAA2BA,GAC3BoN,yBAAwBA,GAExB2B,eAAcA,GACdc,gCAA+BA,GAC/BU,4BAA2BA,GAE3BmO,0BAAyBA,GACzBK,qBAAoBA,GAEpBQ,gBAAeA,GACf0C,iCAAgCA,IAIlC,QAAuB,IAAZ9L,GACT,IAAK,IAAM/S,MAAQ4f,GACbhqB,OAAOM,UAAUgI,eAAejL,KAAK2sB,GAAS5f,KAChDpK,OAAOC,eAAekd,GAAS/S,GAAM,CACnC7M,MAAOysB,GAAQ5f,IACfwT,UAAU,EACV1d,cAAc", "x_google_ignoreList": [1]}