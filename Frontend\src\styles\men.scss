* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
    scroll-behavior: smooth;
}

:root {
    --primary-color: #f5f5f5;
    --secondary-color: #000;
    --accent-color: #ff6464;
    --green-color:rgba(88, 253, 88, 0.877);
    --text-color: #333;
    --light-gray: #f8f8f8;
    --medium-gray: #e0e0e0;
    --dark-gray: #666;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Common Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

section {
    padding: 60px 0;
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 20px;
    font-weight: 600;
    font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
}

h2 {
    font-size: 2.2rem;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
}

button {
    cursor: pointer;
    border: none;
    outline: none;
    transition: var(--transition);
}

main {
    position: relative;
    width: 100%;

    header {
        height: 100vh;
        width: 100%;
        overflow: hidden;

        #cursor {
            height: 2.8%;
            width: 1.5%;
            background-color: #0a0a0a;
            border-radius: 50%;
            position: fixed;
            z-index: 9;
            box-shadow: 0px 0px 10px 1px #000000;
            display: none;
        }

        nav {
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background-color: transparent;
            backdrop-filter: blur(10px);
            opacity: 1;
            position: fixed;
            top: 0;
            left: 0;
            transition: top 0.3s ease-in-out;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
            z-index: 20;

            #logo {
                height: 7%;
                width: 4%;
                margin-left: 1rem;
                margin-right: 10rem;

                img {
                    height: 100%;
                    width: 100%;
                    object-fit: cover;
                }
            }

            #nav-middle {
                display: flex;
                align-items: center;
                gap: 2.5rem;
                justify-content: center;

                a {
                    text-decoration: none;
                    font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                    color: var(--secondary-color);
                    font-weight: 500;
                    position: relative;
                    padding: 5px 0;
                }

                a::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 0;
                    height: 0;
                    transition: var(--transition);
                }

                a:hover::after {
                    width: 0;
                }

                a:hover {
                    color: #11111188;
                }
            }

            #nav-last {
                display: flex;
                gap: 1.4rem;
                align-items: center;
                justify-content: center;
                margin-right: 1rem;

                #search-bar {
                    display: flex;
                    border-radius: 50px;
                    border: 2px solid #111;
                    width: 100%;

                    i {
                        font-size: 1.3em;
                    }

                    .search-icon {
                        margin-left: .2rem;
                        margin-top: .3rem;
                        border-radius: 50% 0 0 50%;
                        background-color: transparent;
                    }

                    #nav-search {
                        border: none;
                        padding: .2rem;
                        width: 10vw;
                        border-radius: 0 50px 50px 0;
                        font-size: 1rem;
                        font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                        outline: none;
                        background-color: transparent;
                        color: black;
                    }

                    #nav-search:active {
                        background-color: transparent;
                    }

                    #nav-search::-webkit-search-cancel-button {
                        cursor: pointer;
                    }
                }

                .cart-icon-container {
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .cart {
                        font-size: 1.5rem;
                        padding: 5px;
                        border-radius: 50%;
                        transition: var(--transition);
                    }

                    .cart-count {
                        position: absolute;
                        top: -8px;
                        right: -8px;
                        background-color: var(--accent-color);
                        color: white;
                        font-size: 0.7rem;
                        font-weight: bold;
                        width: 18px;
                        height: 18px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: transform 0.3s ease;
                    }

                    &.animate .cart-count {
                        animation: cartBounce 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                    }

                    @keyframes cartBounce {
                        0% { transform: scale(1); }
                        25% { transform: scale(2.2); }
                        50% { transform: scale(1.8); }
                        75% { transform: scale(2.0); }
                        100% { transform: scale(1); }
                    }
                }

                .cart-icon-container:hover,
                .cart-icon-container:active {
                    cursor: pointer;

                    .cart {
                        color: #11111188;
                        transform: scale(1.1);
                    }
                }

                .user {
                    font-size: 1.7rem;
                    transition: var(--transition);
                }

                .user:hover {
                    cursor: pointer;
                    color: #11111188;
                    transform: scale(1.1);
                }
            }
        }

        /* Hero Section */
        #hero {
            height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding-top: 80px;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                background: url('/assets/images/pattern-dots.png');
                opacity: 0.05;
                z-index: 0;
            }

            &::after {
                content: '';
                position: absolute;
                bottom: -50px;
                right: -50px;
                width: 300px;
                height: 300px;
                background: radial-gradient(circle, rgba(255, 77, 77, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
                border-radius: 50%;
                z-index: 0;
            }

            .hero-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 20px;
                height: 100%;
                position: relative;
                z-index: 1;

                .hero-text {
                    flex: 1;
                    padding-right: 40px;

                    h1 {
                        font-size: 4rem;
                        margin-bottom: 20px;
                        line-height: 1.1;
                        color: var(--secondary-color);
                        font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                        font-weight: 700;
                        letter-spacing: -0.5px;
                        position: relative;

                        &::after {
                            content: '';
                            position: absolute;
                            bottom: -10px;
                            left: 0;
                            width: 100px;
                            height: 4px;
                            background-color: var(--accent-color);
                        }
                    }

                    p {
                        font-size: 1.3rem;
                        color: var(--dark-gray);
                        margin-bottom: 40px;
                        line-height: 1.6;
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                        max-width: 90%;
                    }

                    .buy-now-btn {
                        background-color: var(--secondary-color);
                        color: white;
                        padding: 15px 35px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        border-radius: 30px;
                        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                        position: relative;
                        overflow: hidden;
                        z-index: 1;
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                        letter-spacing: 0.5px;
                        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                    }

                    .buy-now-btn:active {
                        transform:  scale(1);
                    }
                }

                .hero-image {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: relative;

                    &::before {
                        content: '';
                        position: absolute;
                        width: 400px;
                        height: 400px;
                        background: radial-gradient(circle, rgba(255, 77, 77, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
                        border-radius: 50%;
                        z-index: -1;
                        animation: pulse 6s infinite alternate ease-in-out;
                    }

                    &::after {
                        content: '';
                        position: absolute;
                        width: 300px;
                        height: 300px;
                        background: radial-gradient(circle, rgba(0, 0, 0, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
                        border-radius: 50%;
                        z-index: -1;
                        animation: pulse 8s infinite alternate-reverse ease-in-out;
                    }

                    img {
                        max-width: 100%;
                        height: auto;
                        transform: rotate(-15deg);
                        transition: var(--transition);
                        filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.25));
                        animation: float 6s infinite ease-in-out;
                    }

                    img:hover {
                        transform: rotate(-10deg) scale(1.05);
                    }
                }
            }
        }

        @keyframes float {
            0% {
                transform: rotate(-15deg) translateY(0px);
            }
            50% {
                transform: rotate(-12deg) translateY(-15px);
            }
            100% {
                transform: rotate(-15deg) translateY(0px);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.5;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 0.5;
            }
        }
    }

    /* Men's Collection Section */
    #mens-collection {
        padding: 80px 0;
        background-color: white;

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;

            .product-card {
                background-color: var(--light-gray);
                border-radius: 10px;
                overflow: hidden;
                box-shadow: var(--shadow);
                transition: var(--transition);
                cursor: pointer;
                position: relative;

                &:hover {
                    transform: translateY(-10px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);

                    .product-image img {
                        transform: scale(1.05);
                    }
                }

                .product-image {
                    width: 100%;
                    height: 280px;
                    overflow: hidden;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        transition: transform 0.5s ease;
                    }
                }

                .product-info {
                    padding: 20px;

                    .product-name {
                        font-size: 1.2rem;
                        font-weight: 600;
                        margin-bottom: 10px;
                        color: var(--secondary-color);
                        font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                    }

                    .product-price {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        margin-bottom: 15px;

                        .original-price {
                            font-size: 1rem;
                            color: var(--dark-gray);
                            text-decoration: line-through;
                        }

                        .current-price {
                            font-size: 1.3rem;
                            font-weight: 700;
                            color: var(--secondary-color);
                        }

                        .discount-badge {
                            background-color: var(--accent-color);
                            color: white;
                            padding: 3px 8px;
                            border-radius: 4px;
                            font-size: 0.8rem;
                            font-weight: 600;
                        }
                    }

                    .card-add-to-cart-btn {
                        background-color: var(--secondary-color);
                        color: white;
                        padding: 8px 15px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        border-radius: 5px;
                        border: none;
                        cursor: pointer;
                        transition: var(--transition);
                        width: 100%;
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;

                        &:hover {
                            background-color: #333;
                            transform: translateY(-2px);
                        }

                        &:active {
                            transform: translateY(0);
                        }
                    }
                }
            }
        }
    }

    /* Features Section */
    #features {
        background-color: var(--light-gray);
        padding: 80px 0;

        .features-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            gap: 30px;

            .feature {
                flex: 1 1 250px;
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                text-align: center;
                box-shadow: var(--shadow);
                transition: var(--transition);

                i {
                    font-size: 3rem;
                    color: var(--accent-color);
                    margin-bottom: 20px;
                    display: inline-block;
                }

                h3 {
                    font-size: 1.3rem;
                    margin-bottom: 15px;
                    color: var(--secondary-color);
                    font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                    font-weight: 600;
                }

                p {
                    color: var(--dark-gray);
                    line-height: 1.6;
                    font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                }

                &:hover {
                    transform: translateY(-10px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);

                    i {
                        transform: scale(1.1);
                    }
                }
            }
        }
    }

    /* Gallery and Price Section */
    #gallery-price {
        padding: 80px 0;
        background-color: white;

        .gallery-price-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;

            .gallery-container {
                display: flex;
                justify-content: space-between;
                gap: 40px;
                flex-wrap: wrap;

                .gallery-left {
                    flex: 1 1 400px;
                    display: flex;
                    gap: 15px;

                    .thumbnails.vertical {
                        display: flex;
                        flex-direction: column;
                        gap: 15px;
                        width: 120px;

                        .thumbnail {
                            width: 100px;
                            height: 100px;
                            object-fit: cover;
                            border-radius: 8px;
                            cursor: pointer;
                            opacity: 0.7;
                            transition: var(--transition);
                            border: 2px solid transparent;
                        }

                        .thumbnail:hover {
                            opacity: 1;
                        }

                        .thumbnail.active {
                            opacity: 1;
                            border-color: var(--accent-color);
                        }
                    }

                    .main-image-container {
                        flex: 1;
                        max-width: 450px;
                        border-radius: 10px;
                        overflow: hidden;
                        box-shadow: var(--shadow);
                        display: flex;
                        align-items: center;
                        background-color: #f9f9f9;
                        height: 100%;

                        img {
                            width: 100%;
                            height: auto;
                            display: block;
                            transition: var(--transition);
                        }

                        img:hover {
                            transform: scale(1.03);
                        }
                    }
                }
            }

            .price-container {
                flex: 1 1 400px;
                display: flex;
                flex-direction: column;
                gap: 30px;

                .price-info {
                    background-color: var(--light-gray);
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: var(--shadow);

                    h2 {
                        text-align: left;
                        margin-bottom: 30px;
                    }

                    h2::after {
                        left: 0;
                        transform: none;
                    }

                    .product-title {
                        font-size: 2rem;
                        font-weight: 700;
                        color: var(--secondary-color);
                        margin-bottom: 15px;
                        font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                    }

                    .price-display {
                        display: flex;
                        align-items: center;
                        gap: 15px;
                        margin-bottom: 20px;
                        transition: var(--transition);

                        .original-price {
                            font-size: 1.5rem;
                            color: var(--dark-gray);
                            text-decoration: line-through;
                        }

                        .current-price {
                            font-size: 2.5rem;
                            font-weight: 700;
                            color: var(--secondary-color);
                            font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                        }

                        .discount-badge {
                            background-color: var(--accent-color);
                            color: white;
                            letter-spacing: 1px;
                            padding: 5px 10px;
                            border-radius: 5px;
                            font-weight: 600;
                            font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                        }
                    }

                    .limited-offer {
                        color: var(--accent-color);
                        font-weight: 500;
                        margin-bottom: 30px;
                        font-size: 1.1rem;
                    }

                    .add-to-cart-btn {
                        background-color: var(--secondary-color);
                        color: white;
                        margin-top: .8rem;
                        padding: 15px 40px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        border-radius: 30px;
                        transition: var(--transition);
                        display: inline-block;
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                        letter-spacing: 0.5px;
                        width: 100%;
                        text-align: center;
                    }

                    .add-to-cart-btn:hover {
                        background-color: var(--accent-color);
                        transform: translateY(-3px);
                        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                    }

                    .back-to-collection-btn {
                        background-color: var(--primary-gray);
                        color: #111;
                        padding: 12px 30px;
                        font-size: 1rem;
                        font-weight: 500;
                        border-radius: 30px;
                        transition: all 0.3s ease;
                        display: inline-block;
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                        letter-spacing: 0.5px;
                        width: 100%;
                        text-align: center;
                        margin-top: 15px;
                        border: 2px solid #111;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                        z-index: 1;
                    }

                    .back-to-collection-btn:hover {
                        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
                        background-color: #111;
                        color: white;
                    }

                    .back-to-collection-btn:hover::before {
                        height: 100%;
                    }

                    .back-to-collection-btn:active {
                        transform: translateY(0);
                    }
                }

                .size-selector {
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: var(--shadow);

                    h3 {
                        margin-bottom: 20px;
                        font-size: 1.3rem;
                    }

                    .size-options {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 10px;

                        .size-btn {
                            width: 70px;
                            height: 45px;
                            background-color: var(--light-gray);
                            border: 1px solid var(--medium-gray);
                            border-radius: 5px;
                            font-size: 1rem;
                            transition: var(--transition);
                            font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                        }

                        .size-btn:hover {
                            background-color: var(--medium-gray);
                        }

                        .size-btn.active {
                            background-color: var(--secondary-color);
                            color: white;
                            border-color: var(--secondary-color);
                        }
                    }
                }
            }
        }
    }



    /* Reviews Section */
    #reviews {
        padding: 80px 0;
        background-color: white;

        .reviews-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;

            .review-stats {
                text-align: center;
                margin-bottom: 40px;

                .average-rating {
                    display: inline-flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 20px 40px;
                    background-color: var(--light-gray);
                    border-radius: 10px;
                    box-shadow: var(--shadow);

                    .rating-number {
                        font-size: 3rem;
                        font-weight: 700;
                        color: var(--secondary-color);
                        line-height: 1;
                        margin-bottom: 10px;
                    }

                    .stars {
                        color: #ffc107;
                        font-size: 1.5rem;
                        margin-bottom: 10px;
                    }

                    .total-reviews {
                        color: var(--dark-gray);
                    }
                }
            }

            .review-carousel {
                position: relative;
                height: 250px;
                margin-bottom: 30px;
                overflow: hidden;

                .review-slide {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    opacity: 0;
                    transform: translateX(50px);
                    transition: all 0.5s ease;
                    visibility: hidden;

                    &.active {
                        opacity: 1;
                        transform: translateX(0);
                        visibility: visible;
                    }

                    .review-content {
                        background-color: var(--light-gray);
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: var(--shadow);
                        height: 100%;

                        .reviewer-info {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 15px;

                            .reviewer-name {
                                font-weight: 600;
                                font-size: 1.1rem;
                            }

                            .review-date {
                                color: var(--dark-gray);
                            }
                        }

                        .stars {
                            color: #ffc107;
                            margin-bottom: 15px;
                        }

                        .review-text {
                            color: var(--text-color);
                            line-height: 1.6;
                            font-style: italic;
                        }
                    }
                }
            }

            .carousel-controls {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 20px;

                button {
                    background-color: transparent;
                    border: 1px solid var(--medium-gray);
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: var(--transition);

                    i {
                        font-size: 1.5rem;
                    }

                    &:hover {
                        background-color: var(--secondary-color);
                        color: white;
                        border-color: var(--secondary-color);
                    }
                }

                .carousel-dots {
                    display: flex;
                    gap: 8px;

                    .dot {
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        background-color: var(--medium-gray);
                        cursor: pointer;
                        transition: var(--transition);

                        &.active {
                            background-color: var(--accent-color);
                            transform: scale(1.2);
                        }
                    }
                }
            }
        }
    }
}

/* Add Comment Section */
#add-comment {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

  .comment-form-container {
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--white);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);

    h2 {
      text-align: center;
      color: var(--secondary-color);
      margin-bottom: 1rem;
      font-size: 2.5rem;
      font-weight: 700;
    }

    p {
      text-align: center;
      color: var(--dark-gray);
      margin-bottom: 2.5rem;
      opacity: 0.8;
    }

    .comment-form {
      .form-group {
        margin-bottom: 2rem;

        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 600;
          color: var(--secondary-color);
          font-size: 1.1rem;
        }

        input[type="text"], textarea {
          width: 100%;
          padding: 1rem;
          border: 2px solid var(--light-gray);
          border-radius: var(--border-radius);
          font-size: 1rem;
          transition: var(--transition);
          font-family: inherit;

          &:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(51, 51, 51, 0.1);
          }

          &::placeholder {
            color: var(--dark-gray);
            opacity: 0.6;
          }
        }

        textarea {
          resize: vertical;
          min-height: 120px;
          line-height: 1.6;
        }
      }

      .rating-input {
        display: flex;
        align-items: center;
        gap: 1rem;

        .stars-input {
          display: flex;
          gap: 0.3rem;

          i {
            font-size: 2rem;
            color: var(--light-gray);
            cursor: pointer;
            transition: var(--transition);

            &:hover,
            &.active {
              color: #ffc107;
              transform: scale(1.1);
            }
          }
        }

        .rating-text {
          color: var(--dark-gray);
          font-size: 0.9rem;
          opacity: 0.7;
        }
      }

      .submit-comment-btn {
        width: 100%;
        background: linear-gradient(135deg, var(--secondary-color) 0%, #666 100%);
        color: var(--white);
        border: none;
        padding: 1.2rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;

        i {
          font-size: 1.2rem;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(51, 51, 51, 0.3);
        }

        &:active {
          transform: translateY(0);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }
}

/* Comment Notification Styles */
.comment-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--white);
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  z-index: 10000;
  transform: translateX(400px);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 350px;

  &.show {
    transform: translateX(0);
    opacity: 1;
  }

  &.success {
    border-left: 4px solid #28a745;

    i {
      color: #28a745;
      font-size: 1.2rem;
    }
  }

  &.error {
    border-left: 4px solid #dc3545;

    i {
      color: #dc3545;
      font-size: 1.2rem;
    }
  }

  span {
    color: var(--secondary-color);
    font-weight: 500;
    line-height: 1.4;
  }
}

/* Footer Styles */
footer {
    background-color: var(--secondary-color);
    color: white;
    padding: 60px 0 20px;

    .footer-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 40px;
        margin-bottom: 40px;

        .footer-column {
            flex: 1 1 200px;

            h3 {
                font-size: 1.3rem;
                margin-bottom: 20px;
                position: relative;
                padding-bottom: 10px;
            }

            h3::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 40px;
                height: 2px;
                background-color: var(--accent-color);
            }

            ul {
                list-style: none;

                li {
                    margin-bottom: 10px;

                    a {
                        color: #ccc;
                        text-decoration: none;
                        transition: var(--transition);
                    }

                    a:hover {
                        color: white;
                        padding-left: 5px;
                    }
                }
            }

            &.social {
                flex: 1.5 1 300px;

                .social-icons {
                    display: flex;
                    gap: 15px;
                    margin-bottom: 25px;

                    a {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 40px;
                        height: 40px;
                        background-color: rgba(255, 255, 255, 0.1);
                        border-radius: 50%;
                        color: white;
                        transition: var(--transition);

                        i {
                            font-size: 1.2rem;
                        }

                        &:hover {
                            background-color: var(--accent-color);
                            transform: translateY(-3px);
                        }
                    }
                }

                .newsletter {
                    h4 {
                        font-size: 1.1rem;
                        margin-bottom: 15px;
                    }

                    .newsletter-form {
                        display: flex;
                        gap: 10px;

                        input {
                            flex: 1;
                            padding: 10px 15px;
                            border: none;
                            border-radius: 5px;
                            background-color: rgba(255, 255, 255, 0.1);
                            color: white;
                            outline: none;
                        }

                        input::placeholder {
                            color: #ccc;
                        }

                        button {
                            padding: 10px 20px;
                            background-color: var(--accent-color);
                            color: white;
                            border-radius: 5px;
                            font-weight: 600;
                            transition: var(--transition);
                        }

                        button:hover {
                            background-color: darken(#ff4d4d, 10%);
                        }
                    }
                }
            }
        }
    }

    .footer-bottom {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px 20px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 20px;

        .copyright {
            color: #999;
            font-size: 0.9rem;
        }

        .footer-links {
            display: flex;
            gap: 20px;

            a {
                color: #999;
                text-decoration: none;
                font-size: 0.9rem;
                transition: var(--transition);
            }

            a:hover {
                color: white;
            }
        }
    }
}

/* Cart Modal Styles */
.cart-modal {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100%;
    background-color: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow-y: auto;

    &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: -1;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    &.open {
        right: 0;

        &::before {
            opacity: 1;
            visibility: visible;
        }
    }

    .cart-modal-content {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: white;
        border-radius: 10px 0 0 10px;
    }

    .cart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--secondary-color);
        margin-bottom: 20px;
        position: relative;

        h3 {
            font-size: 1.5rem;
            margin: 0;
            font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
            font-weight: 600;
            position: relative;
            padding-left: 30px;

            &::before {
                content: '🛒';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                font-size: 1.2rem;
            }
        }

        .close-cart-btn {
            background: none;
            border: none;
            font-size: 1.8rem;
            cursor: pointer;
            color: var(--dark-gray);
            transition: var(--transition);
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                color: var(--accent-color);
                background-color: rgba(0, 0, 0, 0.05);
                transform: rotate(90deg);
            }
        }
    }

    .cart-items {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 20px;

        .cart-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--medium-gray);
            transition: var(--transition);

            &:hover {
                background-color: rgba(0, 0, 0, 0.02);
            }

            .cart-item-image {
                width: 80px;
                height: 80px;
                border-radius: 8px;
                overflow: hidden;
                margin-right: 15px;
                background-color: #f8f8f8;
                padding: 5px;
                border: 1px solid var(--medium-gray);

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    transition: transform 0.3s ease;
                }

                &:hover img {
                    transform: scale(1.1);
                }
            }

            .cart-item-details {
                flex: 1;

                .cart-item-name {
                    font-weight: 600;
                    margin-bottom: 5px;
                    font-size: 1rem;
                }

                .cart-item-price {
                    color: var(--secondary-color);
                    font-weight: 600;
                    margin-bottom: 12px;
                }

                .cart-item-controls {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .quantity-control {
                        display: flex;
                        align-items: center;
                        background-color: var(--light-gray);
                        border-radius: 20px;
                        padding: 2px;
                        border: 1px solid var(--medium-gray);

                        button {
                            width: 28px;
                            height: 28px;
                            border-radius: 50%;
                            background-color: white;
                            border: 1px solid var(--medium-gray);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 1.1rem;
                            font-weight: bold;
                            transition: var(--transition);
                            cursor: pointer;
                            color: var(--secondary-color);

                            &:hover {
                                background-color: var(--secondary-color);
                                color: white;
                                transform: scale(1.05);
                            }

                            &:active {
                                transform: scale(0.95);
                            }
                        }

                        .quantity {
                            font-weight: 600;
                            width: 30px;
                            text-align: center;
                            font-size: 0.95rem;
                        }
                    }

                    .remove-item {
                        background-color: transparent;
                        border: none;
                        color: var(--dark-gray);
                        cursor: pointer;
                        font-size: 0.85rem;
                        transition: var(--transition);
                        padding: 5px 10px;
                        border-radius: 15px;
                        border: 1px solid transparent;

                        &:hover {
                            color: var(--accent-color);
                            border-color: var(--accent-color);
                        }

                        &:active {
                            transform: scale(0.95);
                        }
                    }
                }
            }
        }

        .empty-cart-message {
            text-align: center;
            padding: 50px 0;
            color: var(--dark-gray);
            font-size: 1.1rem;
            font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
            position: relative;

            &::before {
                content: '🛒';
                display: block;
                font-size: 3rem;
                margin-bottom: 15px;
                opacity: 0.5;
            }

            &::after {
                content: '';
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                width: 50px;
                height: 2px;
                background-color: var(--medium-gray);
            }
        }
    }

    .cart-footer {
        padding-top: 15px;
        border-top: 2px solid var(--secondary-color);
        background-color: #f9f9f9;
        padding: 20px;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.05);

        .cart-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 1.2rem;
            font-weight: 600;
            font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;

            span:first-child {
                color: var(--dark-gray);
            }

            .total-amount {
                color: var(--secondary-color);
                font-size: 1.4rem;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -5px;
                    left: 0;
                    width: 100%;
                    height: 2px;
                    background-color: var(--accent-color);
                    transform: scaleX(0);
                    transition: transform 0.3s ease;
                    transform-origin: right;
                }

                &:hover::after {
                    transform: scaleX(1);
                    transform-origin: left;
                }
            }
        }

        .checkout-btn {
            width: 100%;
            padding: 15px;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            letter-spacing: 0.5px;
            font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;

            &::after {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: -100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: all 0.5s ease;
            }

            &:hover {
                background-color: var(--accent-color);
                transform: translateY(-3px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);

                &::after {
                    left: 100%;
                }
            }

            &:active {
                transform: translateY(0);
            }
        }
    }
}

/* Toast Notification Styles */
.toast-notification {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: var(--secondary-color);
    color: white;
    padding: 15px 25px;
    border-radius: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    transform: translateY(100px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;

    &.success {
        background-color: #4CAF50;
    }

    &.error {
        background-color: #F44336;
    }

    &.show {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .toast-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .toast-icon {
        font-size: 1.5rem;
        color: rgb(255, 255, 255);
    }

    &.success .toast-icon {
        color: #f3f3f3;
    }

    &.error .toast-icon {
        color: rgba(255, 255, 255, 0.9);
    }

    .toast-message {
        font-weight: 500;
    }
}

/* Responsive Styles */
@media (max-width: 1200px) {
    main header #hero .hero-content {
        padding: 0 40px;
    }

    .cart-modal {
        width: 350px;
    }
}

@media (max-width: 992px) {
    main header #hero .hero-content {
        flex-direction: column;
        text-align: center;
        padding-top: 100px;

        .hero-text {
            padding-right: 0;
            margin-bottom: 40px;

            h1 {
                font-size: 3rem;
            }
        }

        .hero-text h2::after {
            left: 50%;
            transform: translateX(-50%);
        }
    }

    main #gallery-price .gallery-price-container .gallery-container .price-container .price-info h2 {
        text-align: center;
    }

    main #gallery-price .gallery-price-container .gallery-container .price-container .price-info h2::after {
        left: 50%;
        transform: translateX(-50%);
    }

    main #gallery-price .gallery-price-container .gallery-container .gallery-left {
        flex-direction: column;
        align-items: center;
    }

    main #gallery-price .gallery-price-container .gallery-container .gallery-left .thumbnails.vertical {
        flex-direction: row;
        width: 100%;
        justify-content: center;
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    main header nav {
        padding: 0.8rem;

        #logo {
            margin-right: 2rem;
        }

        #nav-middle {
            gap: 1.5rem;
        }

        #nav-last #search-bar #nav-search {
            width: 20vw;
        }
    }

    .cart-modal {
        width: 300px;
    }

    main #features .features-container {
        gap: 20px;
    }

    main #gallery-price .gallery-price-container .gallery-container .gallery-left .main-image-container {
        max-width: 100%;
    }

    main #gallery-price .gallery-price-container .gallery-container .gallery-left .thumbnails.vertical .thumbnail {
        width: 80px;
        height: 80px;
    }

    main #gallery .gallery-container .thumbnails .thumbnail {
        width: 80px;
        height: 80px;
    }

    footer .footer-container {
        gap: 30px;
    }
}

@media (max-width: 576px) {
    h2 {
        font-size: 1.8rem;
    }

    .cart-modal {
        width: 100%;
        right: -100%;
    }

    main header #hero .hero-content .hero-text h1 {
        font-size: 2.5rem;
    }

    main header #hero .hero-content .hero-text p {
        font-size: 1rem;
    }

    main #features .features-container .feature {
        padding: 20px;
    }

    main #price .price-container .price-info .price-display {
        flex-wrap: wrap;
    }

    main #reviews .reviews-container .review-carousel {
        height: 300px;
    }

    footer .footer-container .footer-column.social .newsletter .newsletter-form {
        flex-direction: column;
    }

    footer .footer-bottom {
        flex-direction: column;
        text-align: center;

        .footer-links {
            justify-content: center;
        }
    }
}